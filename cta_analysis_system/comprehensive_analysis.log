2025-06-09 22:11:10,673 - __main__ - INFO - 🚀 启动CTA策略分析系统
2025-06-09 22:11:10,673 - __main__ - INFO - ============================================================
2025-06-09 22:11:10,673 - __main__ - INFO - 🔍 验证系统配置和数据...
2025-06-09 22:15:11,861 - comprehensive_analysis - INFO - 📈 Running performance analysis...
2025-06-09 22:15:11,861 - analysis.performance_analyzer - INFO - Analyzing strategy category performance...
2025-06-09 22:15:11,861 - comprehensive_analysis - ERROR - ❌ Performance analysis failed: 'NoneType' object has no attribute 'data'
2025-06-09 22:15:11,862 - comprehensive_analysis - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/PythonProject/cta_analysis_system/comprehensive_analysis.py", line 209, in run_performance_analysis
    strategy_results = analyzer.analyze_strategy_category_performance(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/PythonProject/cta_analysis_system/analysis/performance_analyzer.py", line 296, in analyze_strategy_category_performance
    strategy_col = self.config.data.strategy_category_column
                   ^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'data'

2025-06-09 22:15:11,876 - comprehensive_analysis - INFO - 📈 Running performance analysis...
2025-06-09 22:15:11,876 - analysis.performance_analyzer - INFO - Analyzing strategy category performance...
2025-06-09 22:15:11,876 - comprehensive_analysis - ERROR - ❌ Performance analysis failed: 'NoneType' object has no attribute 'data'
2025-06-09 22:15:11,876 - comprehensive_analysis - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/PythonProject/cta_analysis_system/comprehensive_analysis.py", line 209, in run_performance_analysis
    strategy_results = analyzer.analyze_strategy_category_performance(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/PythonProject/cta_analysis_system/analysis/performance_analyzer.py", line 296, in analyze_strategy_category_performance
    strategy_col = self.config.data.strategy_category_column
                   ^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'data'

2025-06-09 22:15:11,899 - comprehensive_analysis - INFO - ✅ Configuration loaded successfully
2025-06-09 22:15:11,907 - comprehensive_analysis - INFO - 🔍 Loading and validating CTA data...
2025-06-09 22:15:11,908 - comprehensive_analysis - INFO - ✅ CTA data loaded with utf-8 encoding
2025-06-09 22:15:11,908 - comprehensive_analysis - INFO - 📊 CTA data shape: (3, 4)
2025-06-09 22:15:11,908 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-01-03
2025-06-09 22:15:11,909 - comprehensive_analysis - INFO - ✅ Position data loaded successfully
2025-06-09 22:15:11,909 - comprehensive_analysis - INFO - 📊 Position data shape: (270, 3)
2025-06-09 22:15:11,909 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-03-31
2025-06-09 22:15:11,909 - comprehensive_analysis - INFO - 🔍 Validating data quality...
2025-06-09 22:15:11,909 - comprehensive_analysis - INFO - ✅ CTA data dates converted to datetime
2025-06-09 22:15:11,910 - comprehensive_analysis - INFO - ✅ Position data dates converted to datetime
2025-06-09 22:15:11,911 - comprehensive_analysis - INFO - ✅ Filled 1 missing values in CTA data
2025-06-09 22:15:11,911 - comprehensive_analysis - INFO - ✅ Numeric data types validated
2025-06-09 22:15:11,911 - comprehensive_analysis - INFO - ✅ Filtered CTA data from 2025-01-01 to 2025-01-03
2025-06-09 22:15:11,911 - comprehensive_analysis - INFO - ✅ Filtered position data from 2025-01-01
2025-06-09 22:15:11,911 - comprehensive_analysis - INFO - 📊 Final CTA data shape: (2, 4)
2025-06-09 22:15:11,911 - comprehensive_analysis - INFO - 📊 Final position data shape: (270, 3)
2025-06-09 22:15:11,920 - comprehensive_analysis - INFO - 🚀 Starting comprehensive CTA analysis...
2025-06-09 22:15:11,920 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:15:11,920 - comprehensive_analysis - INFO - ✅ Configuration loaded successfully
2025-06-09 22:15:11,920 - comprehensive_analysis - INFO - 🔍 Loading and validating CTA data...
2025-06-09 22:15:11,920 - comprehensive_analysis - INFO - ✅ CTA data loaded with utf-8 encoding
2025-06-09 22:15:11,920 - comprehensive_analysis - INFO - 📊 CTA data shape: (810, 6)
2025-06-09 22:15:11,920 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-03-31
2025-06-09 22:15:11,921 - comprehensive_analysis - INFO - ✅ Position data loaded successfully
2025-06-09 22:15:11,921 - comprehensive_analysis - INFO - 📊 Position data shape: (270, 3)
2025-06-09 22:15:11,921 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-03-31
2025-06-09 22:15:11,921 - comprehensive_analysis - INFO - 🔍 Validating data quality...
2025-06-09 22:15:11,921 - comprehensive_analysis - INFO - ✅ CTA data dates converted to datetime
2025-06-09 22:15:11,922 - comprehensive_analysis - INFO - ✅ Position data dates converted to datetime
2025-06-09 22:15:11,923 - comprehensive_analysis - INFO - ✅ Numeric data types validated
2025-06-09 22:15:11,923 - comprehensive_analysis - INFO - ✅ Filtered CTA data from 2025-01-01 to 2025-03-31
2025-06-09 22:15:11,923 - comprehensive_analysis - INFO - ✅ Filtered position data from 2025-01-01
2025-06-09 22:15:11,923 - comprehensive_analysis - INFO - 📊 Final CTA data shape: (810, 6)
2025-06-09 22:15:11,923 - comprehensive_analysis - INFO - 📊 Final position data shape: (270, 3)
2025-06-09 22:15:11,923 - comprehensive_analysis - INFO - 📈 Running performance analysis...
2025-06-09 22:15:11,923 - analysis.performance_analyzer - INFO - Analyzing strategy category performance...
2025-06-09 22:15:11,926 - analysis.performance_analyzer - WARNING - Found 2 outlier returns, capping them
2025-06-09 22:15:11,929 - analysis.performance_analyzer - WARNING - Found 1 outlier returns, capping them
2025-06-09 22:15:11,932 - analysis.performance_analyzer - INFO - Calculating time window performance with calendar periods...
2025-06-09 22:15:11,933 - analysis.performance_analyzer - INFO - Time window calculation summary:
时间窗口计算方法: 日历期间
数据最新日期: 2025-03-31 00:00:00

时间窗口边界:
  本日 (2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本周 (2025-03-31 至 2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本月 (2025-03): 2025-03-01 至 2025-03-31 (31天)
  本季度 (Q1 2025): 2025-01-01 至 2025-03-31 (90天)
  本年 (2025): 2025-01-01 至 2025-03-31 (90天)
2025-06-09 22:15:11,938 - comprehensive_analysis - INFO - ✅ Performance analysis completed
2025-06-09 22:15:11,938 - comprehensive_analysis - INFO - ⚠️ Running risk analysis...
2025-06-09 22:15:11,938 - analysis.risk_analyzer - INFO - Running comprehensive risk analysis...
2025-06-09 22:15:11,938 - analysis.risk_analyzer - INFO - Calculating historical VaR and ES...
2025-06-09 22:15:11,938 - analysis.risk_analyzer - WARNING - Insufficient data for trend: 90 < 250
2025-06-09 22:15:11,938 - analysis.risk_analyzer - WARNING - Insufficient data for option: 90 < 250
2025-06-09 22:15:11,938 - analysis.risk_analyzer - WARNING - Insufficient data for other: 90 < 250
2025-06-09 22:15:11,938 - analysis.risk_analyzer - INFO - Identifying risk concentrations...
2025-06-09 22:15:11,938 - analysis.risk_analyzer - INFO - Calculating correlation matrix...
2025-06-09 22:15:11,938 - analysis.risk_analyzer - INFO - Calculating tail risk metrics...
2025-06-09 22:15:11,940 - analysis.risk_analyzer - INFO - Calculating stress test scenarios...
2025-06-09 22:15:11,941 - analysis.risk_analyzer - INFO - Calculating risk-adjusted returns...
2025-06-09 22:15:11,941 - analysis.risk_analyzer - INFO - Validating risk metrics...
2025-06-09 22:15:11,941 - analysis.risk_analyzer - INFO - Risk metrics validation completed: 3/3 strategies passed validation
2025-06-09 22:15:11,941 - analysis.risk_analyzer - WARNING - Found 6 warnings
2025-06-09 22:15:11,941 - analysis.risk_analyzer - INFO - Adding drawdown analysis...
2025-06-09 22:15:11,941 - analysis.drawdown_analyzer - INFO - Analyzing drawdown periods...
2025-06-09 22:15:11,941 - analysis.drawdown_analyzer - INFO - Analyzing portfolio drawdowns...
2025-06-09 22:15:11,941 - analysis.drawdown_analyzer - INFO - Analyzing strategy drawdowns...
2025-06-09 22:15:11,944 - analysis.drawdown_analyzer - INFO - Analyzing time dimension drawdowns...
2025-06-09 22:15:11,944 - analysis.drawdown_analyzer - INFO - Analyzing monthly drawdowns...
2025-06-09 22:15:11,957 - analysis.drawdown_analyzer - INFO - Analyzing yearly drawdowns...
2025-06-09 22:15:11,958 - analysis.drawdown_analyzer - INFO - Generating drawdown summary...
2025-06-09 22:15:11,960 - analysis.risk_analyzer - INFO - Generating risk summary...
2025-06-09 22:15:11,960 - comprehensive_analysis - INFO - ✅ Risk validation: 3/3 strategies passed
2025-06-09 22:15:11,960 - comprehensive_analysis - WARNING - ⚠️ 6 risk warnings found
2025-06-09 22:15:11,960 - comprehensive_analysis - INFO - ✅ Risk analysis completed
2025-06-09 22:15:11,960 - comprehensive_analysis - INFO - 📊 Running contribution analysis...
2025-06-09 22:15:11,960 - analysis.contribution_analyzer - INFO - Calculating time window contributions with calendar periods...
2025-06-09 22:15:11,961 - analysis.contribution_analyzer - INFO - Time window calculation for contributions:
时间窗口计算方法: 日历期间
数据最新日期: 2025-03-31 00:00:00

时间窗口边界:
  本日 (2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本周 (2025-03-31 至 2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本月 (2025-03): 2025-03-01 至 2025-03-31 (31天)
  本季度 (Q1 2025): 2025-01-01 至 2025-03-31 (90天)
  本年 (2025): 2025-01-01 至 2025-03-31 (90天)
2025-06-09 22:15:11,961 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:15:11,962 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:15:11,962 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:15:11,963 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:15:11,963 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:15:11,964 - comprehensive_analysis - INFO - ✅ Contribution analysis completed
2025-06-09 22:15:11,964 - comprehensive_analysis - INFO - 📋 Generating comprehensive reports...
2025-06-09 22:15:12,079 - reporting.excel_reporter - INFO - Generating comprehensive Excel report...
2025-06-09 22:15:12,100 - reporting.excel_reporter - INFO - Excel report saved: reports/cta_analysis_report_20250609_221512.xlsx
2025-06-09 22:15:12,100 - comprehensive_analysis - INFO - ✅ Excel report generated: reports/cta_analysis_report_20250609_221512.xlsx
2025-06-09 22:15:12,670 - dashboard.dashboard_generator - INFO - Generating comprehensive dashboard...
2025-06-09 22:15:12,670 - dashboard.dashboard_generator - INFO - Generating static dashboard...
2025-06-09 22:15:13,305 - dashboard.dashboard_generator - INFO - Static dashboard saved: output/cta_analysis/dashboard/cta_dashboard_20250609_221512.png
2025-06-09 22:15:13,305 - dashboard.dashboard_generator - INFO - Generating interactive dashboard...
2025-06-09 22:15:13,370 - dashboard.dashboard_generator - WARNING - Failed to generate interactive dashboard: Trace type 'table' is not compatible with subplot type 'xy'
at grid position (1, 1)

See the docstring for the specs argument to plotly.subplots.make_subplots
for more information on subplot types
2025-06-09 22:15:13,370 - comprehensive_analysis - INFO - ✅ static_dashboard dashboard generated: output/cta_analysis/dashboard/cta_dashboard_20250609_221512.png
2025-06-09 22:15:13,370 - reporting.markdown_reporter - INFO - Generating comprehensive Markdown report...
2025-06-09 22:15:13,370 - reporting.markdown_reporter - INFO - Markdown report saved: reports/cta_analysis_report_20250609_221513.md
2025-06-09 22:15:13,370 - comprehensive_analysis - INFO - ✅ Markdown report generated: reports/cta_analysis_report_20250609_221513.md
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - 🔍 Validating system enhancements...
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - ✅ Calendar-based time windows validated
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - ✅ Risk metrics validation working
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - ✅ Return rate calculations validated
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - ✅ Daily performance summary in outputs
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - 📝 Generating execution summary...
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - 🎯 COMPREHENSIVE ANALYSIS COMPLETED
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - 📊 Data Processed:
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO -    CTA Records: 810
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO -    Position Records: 270
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO -    Date Range: 2025-01-01 00:00:00 to 2025-03-31 00:00:00
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - 
📈 Analysis Modules:
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO -    ✅ Performance Analysis
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO -    ✅ Risk Analysis
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO -    ✅ Contribution Analysis
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - 
📋 Outputs Generated: 3
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO -    📄 Excel Report: reports/cta_analysis_report_20250609_221512.xlsx
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO -    📄 Static_Dashboard: output/cta_analysis/dashboard/cta_dashboard_20250609_221512.png
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO -    📄 Markdown Report: reports/cta_analysis_report_20250609_221513.md
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - 
🔍 Enhancement Validation:
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO -    ✅ Calendar Time Windows
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO -    ✅ Risk Validation
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO -    ✅ Return Rate Calculations
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO -    ✅ Daily Performance Summary
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - 
🔧 Resolutions Applied (1):
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO -    1. Fixed 3 data quality issues
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - 
🎉 Analysis COMPLETED SUCCESSFULLY
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - 🔍 Validating system enhancements...
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - ✅ Calendar-based time windows validated
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - ✅ Risk metrics validation working
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - ✅ Return rate calculations validated
2025-06-09 22:15:13,371 - comprehensive_analysis - INFO - ✅ Daily performance summary in outputs
2025-06-09 22:15:13,379 - comprehensive_analysis - INFO - 🔍 Loading and validating CTA data...
2025-06-09 22:15:13,379 - comprehensive_analysis - ERROR - ❌ Failed to load CTA data: [Errno 2] No such file or directory: 'processed_cta_data.csv'
2025-06-09 22:15:13,387 - comprehensive_analysis - INFO - 🚀 Starting comprehensive CTA analysis...
2025-06-09 22:15:13,387 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:15:13,387 - comprehensive_analysis - INFO - ✅ Configuration loaded successfully
2025-06-09 22:15:13,387 - comprehensive_analysis - INFO - 🔍 Loading and validating CTA data...
2025-06-09 22:15:13,387 - comprehensive_analysis - INFO - ✅ CTA data loaded with utf-8 encoding
2025-06-09 22:15:13,387 - comprehensive_analysis - INFO - 📊 CTA data shape: (810, 6)
2025-06-09 22:15:13,388 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-03-31
2025-06-09 22:15:13,388 - comprehensive_analysis - INFO - ✅ Position data loaded successfully
2025-06-09 22:15:13,388 - comprehensive_analysis - INFO - 📊 Position data shape: (270, 3)
2025-06-09 22:15:13,388 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-03-31
2025-06-09 22:15:13,388 - comprehensive_analysis - INFO - 🔍 Validating data quality...
2025-06-09 22:15:13,389 - comprehensive_analysis - INFO - ✅ CTA data dates converted to datetime
2025-06-09 22:15:13,389 - comprehensive_analysis - INFO - ✅ Position data dates converted to datetime
2025-06-09 22:15:13,390 - comprehensive_analysis - INFO - ✅ Numeric data types validated
2025-06-09 22:15:13,390 - comprehensive_analysis - INFO - ✅ Filtered CTA data from 2025-01-01 to 2025-03-31
2025-06-09 22:15:13,390 - comprehensive_analysis - INFO - ✅ Filtered position data from 2025-01-01
2025-06-09 22:15:13,390 - comprehensive_analysis - INFO - 📊 Final CTA data shape: (810, 6)
2025-06-09 22:15:13,390 - comprehensive_analysis - INFO - 📊 Final position data shape: (270, 3)
2025-06-09 22:15:13,390 - comprehensive_analysis - INFO - 📈 Running performance analysis...
2025-06-09 22:15:13,390 - analysis.performance_analyzer - INFO - Analyzing strategy category performance...
2025-06-09 22:15:13,397 - analysis.performance_analyzer - INFO - Calculating time window performance with calendar periods...
2025-06-09 22:15:13,398 - analysis.performance_analyzer - INFO - Time window calculation summary:
时间窗口计算方法: 日历期间
数据最新日期: 2025-03-31 00:00:00

时间窗口边界:
  本日 (2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本周 (2025-03-31 至 2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本月 (2025-03): 2025-03-01 至 2025-03-31 (31天)
  本季度 (Q1 2025): 2025-01-01 至 2025-03-31 (90天)
  本年 (2025): 2025-01-01 至 2025-03-31 (90天)
2025-06-09 22:15:13,403 - comprehensive_analysis - INFO - ✅ Performance analysis completed
2025-06-09 22:15:13,403 - comprehensive_analysis - INFO - ⚠️ Running risk analysis...
2025-06-09 22:15:13,403 - analysis.risk_analyzer - INFO - Running comprehensive risk analysis...
2025-06-09 22:15:13,403 - analysis.risk_analyzer - INFO - Calculating historical VaR and ES...
2025-06-09 22:15:13,403 - analysis.risk_analyzer - WARNING - Insufficient data for trend: 90 < 250
2025-06-09 22:15:13,403 - analysis.risk_analyzer - WARNING - Insufficient data for option: 90 < 250
2025-06-09 22:15:13,403 - analysis.risk_analyzer - WARNING - Insufficient data for other: 90 < 250
2025-06-09 22:15:13,403 - analysis.risk_analyzer - INFO - Identifying risk concentrations...
2025-06-09 22:15:13,403 - analysis.risk_analyzer - INFO - Calculating correlation matrix...
2025-06-09 22:15:13,404 - analysis.risk_analyzer - INFO - Calculating tail risk metrics...
2025-06-09 22:15:13,406 - analysis.risk_analyzer - INFO - Calculating stress test scenarios...
2025-06-09 22:15:13,406 - analysis.risk_analyzer - INFO - Calculating risk-adjusted returns...
2025-06-09 22:15:13,406 - analysis.risk_analyzer - INFO - Validating risk metrics...
2025-06-09 22:15:13,406 - analysis.risk_analyzer - INFO - Risk metrics validation completed: 3/3 strategies passed validation
2025-06-09 22:15:13,406 - analysis.risk_analyzer - WARNING - Found 8 warnings
2025-06-09 22:15:13,406 - analysis.risk_analyzer - INFO - Adding drawdown analysis...
2025-06-09 22:15:13,406 - analysis.drawdown_analyzer - INFO - Analyzing drawdown periods...
2025-06-09 22:15:13,406 - analysis.drawdown_analyzer - INFO - Analyzing portfolio drawdowns...
2025-06-09 22:15:13,407 - analysis.drawdown_analyzer - INFO - Analyzing strategy drawdowns...
2025-06-09 22:15:13,409 - analysis.drawdown_analyzer - INFO - Analyzing time dimension drawdowns...
2025-06-09 22:15:13,410 - analysis.drawdown_analyzer - INFO - Analyzing monthly drawdowns...
2025-06-09 22:15:13,410 - analysis.drawdown_analyzer - INFO - Analyzing yearly drawdowns...
2025-06-09 22:15:13,411 - analysis.drawdown_analyzer - INFO - Generating drawdown summary...
2025-06-09 22:15:13,411 - analysis.risk_analyzer - INFO - Generating risk summary...
2025-06-09 22:15:13,411 - comprehensive_analysis - INFO - ✅ Risk validation: 3/3 strategies passed
2025-06-09 22:15:13,411 - comprehensive_analysis - WARNING - ⚠️ 8 risk warnings found
2025-06-09 22:15:13,411 - comprehensive_analysis - INFO - ✅ Risk analysis completed
2025-06-09 22:15:13,411 - comprehensive_analysis - INFO - 📊 Running contribution analysis...
2025-06-09 22:15:13,411 - analysis.contribution_analyzer - INFO - Calculating time window contributions with calendar periods...
2025-06-09 22:15:13,412 - analysis.contribution_analyzer - INFO - Time window calculation for contributions:
时间窗口计算方法: 日历期间
数据最新日期: 2025-03-31 00:00:00

时间窗口边界:
  本日 (2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本周 (2025-03-31 至 2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本月 (2025-03): 2025-03-01 至 2025-03-31 (31天)
  本季度 (Q1 2025): 2025-01-01 至 2025-03-31 (90天)
  本年 (2025): 2025-01-01 至 2025-03-31 (90天)
2025-06-09 22:15:13,412 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:15:13,413 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:15:13,413 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:15:13,414 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:15:13,414 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:15:13,415 - comprehensive_analysis - INFO - ✅ Contribution analysis completed
2025-06-09 22:15:13,415 - comprehensive_analysis - INFO - 📋 Generating comprehensive reports...
2025-06-09 22:15:13,415 - reporting.excel_reporter - INFO - Generating comprehensive Excel report...
2025-06-09 22:15:13,439 - reporting.excel_reporter - INFO - Excel report saved: reports/cta_analysis_report_20250609_221513.xlsx
2025-06-09 22:15:13,439 - comprehensive_analysis - INFO - ✅ Excel report generated: reports/cta_analysis_report_20250609_221513.xlsx
2025-06-09 22:15:13,440 - dashboard.dashboard_generator - INFO - Generating comprehensive dashboard...
2025-06-09 22:15:13,440 - dashboard.dashboard_generator - INFO - Generating static dashboard...
2025-06-09 22:15:13,880 - dashboard.dashboard_generator - INFO - Static dashboard saved: output/cta_analysis/dashboard/cta_dashboard_20250609_221513.png
2025-06-09 22:15:13,880 - dashboard.dashboard_generator - INFO - Generating interactive dashboard...
2025-06-09 22:15:13,887 - dashboard.dashboard_generator - WARNING - Failed to generate interactive dashboard: Trace type 'table' is not compatible with subplot type 'xy'
at grid position (1, 1)

See the docstring for the specs argument to plotly.subplots.make_subplots
for more information on subplot types
2025-06-09 22:15:13,887 - comprehensive_analysis - INFO - ✅ static_dashboard dashboard generated: output/cta_analysis/dashboard/cta_dashboard_20250609_221513.png
2025-06-09 22:15:13,887 - reporting.markdown_reporter - INFO - Generating comprehensive Markdown report...
2025-06-09 22:15:13,888 - reporting.markdown_reporter - INFO - Markdown report saved: reports/cta_analysis_report_20250609_221513.md
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - ✅ Markdown report generated: reports/cta_analysis_report_20250609_221513.md
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - 🔍 Validating system enhancements...
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - ✅ Calendar-based time windows validated
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - ✅ Risk metrics validation working
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - ✅ Return rate calculations validated
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - ✅ Daily performance summary in outputs
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - 📝 Generating execution summary...
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - 🎯 COMPREHENSIVE ANALYSIS COMPLETED
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - 📊 Data Processed:
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO -    CTA Records: 810
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO -    Position Records: 270
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO -    Date Range: 2025-01-01 00:00:00 to 2025-03-31 00:00:00
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - 
📈 Analysis Modules:
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO -    ✅ Performance Analysis
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO -    ✅ Risk Analysis
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO -    ✅ Contribution Analysis
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - 
📋 Outputs Generated: 3
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO -    📄 Excel Report: reports/cta_analysis_report_20250609_221513.xlsx
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO -    📄 Static_Dashboard: output/cta_analysis/dashboard/cta_dashboard_20250609_221513.png
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO -    📄 Markdown Report: reports/cta_analysis_report_20250609_221513.md
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - 
🔍 Enhancement Validation:
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO -    ✅ Calendar Time Windows
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO -    ✅ Risk Validation
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO -    ✅ Return Rate Calculations
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO -    ✅ Daily Performance Summary
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - 
🔧 Resolutions Applied (1):
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO -    1. Fixed 3 data quality issues
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - 
🎉 Analysis COMPLETED SUCCESSFULLY
2025-06-09 22:15:13,888 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:15:13,897 - comprehensive_analysis - INFO - 🚀 Starting comprehensive CTA analysis...
2025-06-09 22:15:13,897 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:15:13,897 - comprehensive_analysis - INFO - ✅ Configuration loaded successfully
2025-06-09 22:15:13,897 - comprehensive_analysis - INFO - 🔍 Loading and validating CTA data...
2025-06-09 22:15:13,897 - comprehensive_analysis - INFO - ✅ CTA data loaded with utf-8 encoding
2025-06-09 22:15:13,897 - comprehensive_analysis - INFO - 📊 CTA data shape: (810, 6)
2025-06-09 22:15:13,898 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-03-31
2025-06-09 22:15:13,898 - comprehensive_analysis - INFO - ✅ Position data loaded successfully
2025-06-09 22:15:13,898 - comprehensive_analysis - INFO - 📊 Position data shape: (270, 3)
2025-06-09 22:15:13,898 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-03-31
2025-06-09 22:15:13,898 - comprehensive_analysis - INFO - 🔍 Validating data quality...
2025-06-09 22:15:13,898 - comprehensive_analysis - INFO - ✅ CTA data dates converted to datetime
2025-06-09 22:15:13,899 - comprehensive_analysis - INFO - ✅ Position data dates converted to datetime
2025-06-09 22:15:13,900 - comprehensive_analysis - INFO - ✅ Numeric data types validated
2025-06-09 22:15:13,900 - comprehensive_analysis - INFO - ✅ Filtered CTA data from 2025-01-01 to 2025-03-31
2025-06-09 22:15:13,900 - comprehensive_analysis - INFO - ✅ Filtered position data from 2025-01-01
2025-06-09 22:15:13,900 - comprehensive_analysis - INFO - 📊 Final CTA data shape: (810, 6)
2025-06-09 22:15:13,900 - comprehensive_analysis - INFO - 📊 Final position data shape: (270, 3)
2025-06-09 22:15:13,900 - comprehensive_analysis - INFO - 📈 Running performance analysis...
2025-06-09 22:15:13,900 - analysis.performance_analyzer - INFO - Analyzing strategy category performance...
2025-06-09 22:15:13,905 - analysis.performance_analyzer - WARNING - Found 1 outlier returns, capping them
2025-06-09 22:15:13,907 - analysis.performance_analyzer - INFO - Calculating time window performance with calendar periods...
2025-06-09 22:15:13,907 - analysis.performance_analyzer - INFO - Time window calculation summary:
时间窗口计算方法: 日历期间
数据最新日期: 2025-03-31 00:00:00

时间窗口边界:
  本日 (2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本周 (2025-03-31 至 2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本月 (2025-03): 2025-03-01 至 2025-03-31 (31天)
  本季度 (Q1 2025): 2025-01-01 至 2025-03-31 (90天)
  本年 (2025): 2025-01-01 至 2025-03-31 (90天)
2025-06-09 22:15:13,912 - comprehensive_analysis - INFO - ✅ Performance analysis completed
2025-06-09 22:15:13,912 - comprehensive_analysis - INFO - ⚠️ Running risk analysis...
2025-06-09 22:15:13,912 - analysis.risk_analyzer - INFO - Running comprehensive risk analysis...
2025-06-09 22:15:13,912 - analysis.risk_analyzer - INFO - Calculating historical VaR and ES...
2025-06-09 22:15:13,912 - analysis.risk_analyzer - WARNING - Insufficient data for trend: 90 < 250
2025-06-09 22:15:13,912 - analysis.risk_analyzer - WARNING - Insufficient data for option: 90 < 250
2025-06-09 22:15:13,912 - analysis.risk_analyzer - WARNING - Insufficient data for other: 90 < 250
2025-06-09 22:15:13,912 - analysis.risk_analyzer - INFO - Identifying risk concentrations...
2025-06-09 22:15:13,912 - analysis.risk_analyzer - INFO - Calculating correlation matrix...
2025-06-09 22:15:13,913 - analysis.risk_analyzer - INFO - Calculating tail risk metrics...
2025-06-09 22:15:13,914 - analysis.risk_analyzer - INFO - Calculating stress test scenarios...
2025-06-09 22:15:13,915 - analysis.risk_analyzer - INFO - Calculating risk-adjusted returns...
2025-06-09 22:15:13,915 - analysis.risk_analyzer - INFO - Validating risk metrics...
2025-06-09 22:15:13,915 - analysis.risk_analyzer - INFO - Risk metrics validation completed: 3/3 strategies passed validation
2025-06-09 22:15:13,915 - analysis.risk_analyzer - WARNING - Found 8 warnings
2025-06-09 22:15:13,915 - analysis.risk_analyzer - INFO - Adding drawdown analysis...
2025-06-09 22:15:13,915 - analysis.drawdown_analyzer - INFO - Analyzing drawdown periods...
2025-06-09 22:15:13,915 - analysis.drawdown_analyzer - INFO - Analyzing portfolio drawdowns...
2025-06-09 22:15:13,916 - analysis.drawdown_analyzer - INFO - Analyzing strategy drawdowns...
2025-06-09 22:15:13,918 - analysis.drawdown_analyzer - INFO - Analyzing time dimension drawdowns...
2025-06-09 22:15:13,918 - analysis.drawdown_analyzer - INFO - Analyzing monthly drawdowns...
2025-06-09 22:15:13,919 - analysis.drawdown_analyzer - INFO - Analyzing yearly drawdowns...
2025-06-09 22:15:13,920 - analysis.drawdown_analyzer - INFO - Generating drawdown summary...
2025-06-09 22:15:13,920 - analysis.risk_analyzer - INFO - Generating risk summary...
2025-06-09 22:15:13,920 - comprehensive_analysis - INFO - ✅ Risk validation: 3/3 strategies passed
2025-06-09 22:15:13,920 - comprehensive_analysis - WARNING - ⚠️ 8 risk warnings found
2025-06-09 22:15:13,920 - comprehensive_analysis - INFO - ✅ Risk analysis completed
2025-06-09 22:15:13,920 - comprehensive_analysis - INFO - 📊 Running contribution analysis...
2025-06-09 22:15:13,920 - analysis.contribution_analyzer - INFO - Calculating time window contributions with calendar periods...
2025-06-09 22:15:13,920 - analysis.contribution_analyzer - INFO - Time window calculation for contributions:
时间窗口计算方法: 日历期间
数据最新日期: 2025-03-31 00:00:00

时间窗口边界:
  本日 (2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本周 (2025-03-31 至 2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本月 (2025-03): 2025-03-01 至 2025-03-31 (31天)
  本季度 (Q1 2025): 2025-01-01 至 2025-03-31 (90天)
  本年 (2025): 2025-01-01 至 2025-03-31 (90天)
2025-06-09 22:15:13,921 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:15:13,921 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:15:13,922 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:15:13,923 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:15:13,923 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:15:13,923 - comprehensive_analysis - INFO - ✅ Contribution analysis completed
2025-06-09 22:15:13,923 - comprehensive_analysis - INFO - 📋 Generating comprehensive reports...
2025-06-09 22:15:13,924 - reporting.excel_reporter - INFO - Generating comprehensive Excel report...
2025-06-09 22:15:13,942 - reporting.excel_reporter - INFO - Excel report saved: reports/cta_analysis_report_20250609_221513.xlsx
2025-06-09 22:15:13,942 - comprehensive_analysis - INFO - ✅ Excel report generated: reports/cta_analysis_report_20250609_221513.xlsx
2025-06-09 22:15:13,943 - dashboard.dashboard_generator - INFO - Generating comprehensive dashboard...
2025-06-09 22:15:13,943 - dashboard.dashboard_generator - INFO - Generating static dashboard...
2025-06-09 22:15:14,395 - dashboard.dashboard_generator - INFO - Static dashboard saved: output/cta_analysis/dashboard/cta_dashboard_20250609_221514.png
2025-06-09 22:15:14,395 - dashboard.dashboard_generator - INFO - Generating interactive dashboard...
2025-06-09 22:15:14,403 - dashboard.dashboard_generator - WARNING - Failed to generate interactive dashboard: Trace type 'table' is not compatible with subplot type 'xy'
at grid position (1, 1)

See the docstring for the specs argument to plotly.subplots.make_subplots
for more information on subplot types
2025-06-09 22:15:14,403 - comprehensive_analysis - INFO - ✅ static_dashboard dashboard generated: output/cta_analysis/dashboard/cta_dashboard_20250609_221514.png
2025-06-09 22:15:14,403 - reporting.markdown_reporter - INFO - Generating comprehensive Markdown report...
2025-06-09 22:15:14,403 - reporting.markdown_reporter - INFO - Markdown report saved: reports/cta_analysis_report_20250609_221514.md
2025-06-09 22:15:14,403 - comprehensive_analysis - INFO - ✅ Markdown report generated: reports/cta_analysis_report_20250609_221514.md
2025-06-09 22:15:14,403 - comprehensive_analysis - INFO - 🔍 Validating system enhancements...
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO - ✅ Calendar-based time windows validated
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO - ✅ Risk metrics validation working
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO - ✅ Return rate calculations validated
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO - ✅ Daily performance summary in outputs
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO - 📝 Generating execution summary...
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO - 🎯 COMPREHENSIVE ANALYSIS COMPLETED
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO - 📊 Data Processed:
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO -    CTA Records: 810
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO -    Position Records: 270
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO -    Date Range: 2025-01-01 00:00:00 to 2025-03-31 00:00:00
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO - 
📈 Analysis Modules:
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO -    ✅ Performance Analysis
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO -    ✅ Risk Analysis
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO -    ✅ Contribution Analysis
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO - 
📋 Outputs Generated: 3
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO -    📄 Excel Report: reports/cta_analysis_report_20250609_221513.xlsx
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO -    📄 Static_Dashboard: output/cta_analysis/dashboard/cta_dashboard_20250609_221514.png
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO -    📄 Markdown Report: reports/cta_analysis_report_20250609_221514.md
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO - 
🔍 Enhancement Validation:
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO -    ✅ Calendar Time Windows
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO -    ✅ Risk Validation
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO -    ✅ Return Rate Calculations
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO -    ✅ Daily Performance Summary
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO - 
🔧 Resolutions Applied (1):
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO -    1. Fixed 3 data quality issues
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO - 
🎉 Analysis COMPLETED SUCCESSFULLY
2025-06-09 22:15:14,404 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:15:14,406 - comprehensive_analysis - INFO - ✅ Configuration loaded successfully
2025-06-09 22:17:18,354 - comprehensive_analysis - INFO - 📈 Running performance analysis...
2025-06-09 22:17:18,354 - analysis.performance_analyzer - INFO - Analyzing strategy category performance...
2025-06-09 22:17:18,354 - comprehensive_analysis - ERROR - ❌ Performance analysis failed: 'NoneType' object has no attribute 'data'
2025-06-09 22:17:18,355 - comprehensive_analysis - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/PythonProject/cta_analysis_system/comprehensive_analysis.py", line 209, in run_performance_analysis
    strategy_results = analyzer.analyze_strategy_category_performance(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/PythonProject/cta_analysis_system/analysis/performance_analyzer.py", line 305, in analyze_strategy_category_performance
    strategy_col = self.config.data.strategy_category_column
                   ^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'data'

2025-06-09 22:17:18,370 - comprehensive_analysis - INFO - 📈 Running performance analysis...
2025-06-09 22:17:18,370 - analysis.performance_analyzer - INFO - Analyzing strategy category performance...
2025-06-09 22:17:18,370 - comprehensive_analysis - ERROR - ❌ Performance analysis failed: 'NoneType' object has no attribute 'data'
2025-06-09 22:17:18,370 - comprehensive_analysis - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/PythonProject/cta_analysis_system/comprehensive_analysis.py", line 209, in run_performance_analysis
    strategy_results = analyzer.analyze_strategy_category_performance(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/PythonProject/cta_analysis_system/analysis/performance_analyzer.py", line 305, in analyze_strategy_category_performance
    strategy_col = self.config.data.strategy_category_column
                   ^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'data'

2025-06-09 22:17:18,386 - comprehensive_analysis - INFO - ✅ Configuration loaded successfully
2025-06-09 22:17:18,394 - comprehensive_analysis - INFO - 🔍 Loading and validating CTA data...
2025-06-09 22:17:18,395 - comprehensive_analysis - INFO - ✅ CTA data loaded with utf-8 encoding
2025-06-09 22:17:18,395 - comprehensive_analysis - INFO - 📊 CTA data shape: (3, 4)
2025-06-09 22:17:18,395 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-01-03
2025-06-09 22:17:18,396 - comprehensive_analysis - INFO - ✅ Position data loaded successfully
2025-06-09 22:17:18,396 - comprehensive_analysis - INFO - 📊 Position data shape: (270, 3)
2025-06-09 22:17:18,396 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-03-31
2025-06-09 22:17:18,396 - comprehensive_analysis - INFO - 🔍 Validating data quality...
2025-06-09 22:17:18,397 - comprehensive_analysis - INFO - ✅ CTA data dates converted to datetime
2025-06-09 22:17:18,399 - comprehensive_analysis - INFO - ✅ Position data dates converted to datetime
2025-06-09 22:17:18,399 - comprehensive_analysis - INFO - ✅ Filled 1 missing values in CTA data
2025-06-09 22:17:18,400 - comprehensive_analysis - INFO - ✅ Numeric data types validated
2025-06-09 22:17:18,400 - comprehensive_analysis - INFO - ✅ Filtered CTA data from 2025-01-01 to 2025-01-03
2025-06-09 22:17:18,400 - comprehensive_analysis - INFO - ✅ Filtered position data from 2025-01-01
2025-06-09 22:17:18,400 - comprehensive_analysis - INFO - 📊 Final CTA data shape: (2, 4)
2025-06-09 22:17:18,400 - comprehensive_analysis - INFO - 📊 Final position data shape: (270, 3)
2025-06-09 22:17:18,409 - comprehensive_analysis - INFO - 🚀 Starting comprehensive CTA analysis...
2025-06-09 22:17:18,409 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:17:18,409 - comprehensive_analysis - INFO - ✅ Configuration loaded successfully
2025-06-09 22:17:18,409 - comprehensive_analysis - INFO - 🔍 Loading and validating CTA data...
2025-06-09 22:17:18,409 - comprehensive_analysis - INFO - ✅ CTA data loaded with utf-8 encoding
2025-06-09 22:17:18,409 - comprehensive_analysis - INFO - 📊 CTA data shape: (810, 6)
2025-06-09 22:17:18,410 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-03-31
2025-06-09 22:17:18,410 - comprehensive_analysis - INFO - ✅ Position data loaded successfully
2025-06-09 22:17:18,410 - comprehensive_analysis - INFO - 📊 Position data shape: (270, 3)
2025-06-09 22:17:18,410 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-03-31
2025-06-09 22:17:18,410 - comprehensive_analysis - INFO - 🔍 Validating data quality...
2025-06-09 22:17:18,411 - comprehensive_analysis - INFO - ✅ CTA data dates converted to datetime
2025-06-09 22:17:18,411 - comprehensive_analysis - INFO - ✅ Position data dates converted to datetime
2025-06-09 22:17:18,412 - comprehensive_analysis - INFO - ✅ Numeric data types validated
2025-06-09 22:17:18,412 - comprehensive_analysis - INFO - ✅ Filtered CTA data from 2025-01-01 to 2025-03-31
2025-06-09 22:17:18,413 - comprehensive_analysis - INFO - ✅ Filtered position data from 2025-01-01
2025-06-09 22:17:18,413 - comprehensive_analysis - INFO - 📊 Final CTA data shape: (810, 6)
2025-06-09 22:17:18,413 - comprehensive_analysis - INFO - 📊 Final position data shape: (270, 3)
2025-06-09 22:17:18,413 - comprehensive_analysis - INFO - 📈 Running performance analysis...
2025-06-09 22:17:18,413 - analysis.performance_analyzer - INFO - Analyzing strategy category performance...
2025-06-09 22:17:18,415 - analysis.performance_analyzer - WARNING - Found 2 outlier returns, capping them
2025-06-09 22:17:18,418 - analysis.performance_analyzer - WARNING - Found 1 outlier returns, capping them
2025-06-09 22:17:18,422 - analysis.performance_analyzer - INFO - Calculating time window performance with calendar periods...
2025-06-09 22:17:18,423 - analysis.performance_analyzer - INFO - Time window calculation summary:
时间窗口计算方法: 日历期间
数据最新日期: 2025-03-31 00:00:00

时间窗口边界:
  本日 (2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本周 (2025-03-31 至 2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本月 (2025-03): 2025-03-01 至 2025-03-31 (31天)
  本季度 (Q1 2025): 2025-01-01 至 2025-03-31 (90天)
  本年 (2025): 2025-01-01 至 2025-03-31 (90天)
2025-06-09 22:17:18,428 - comprehensive_analysis - INFO - ✅ Performance analysis completed
2025-06-09 22:17:18,428 - comprehensive_analysis - INFO - ⚠️ Running risk analysis...
2025-06-09 22:17:18,428 - analysis.risk_analyzer - INFO - Running comprehensive risk analysis...
2025-06-09 22:17:18,428 - analysis.risk_analyzer - INFO - Calculating historical VaR and ES...
2025-06-09 22:17:18,428 - analysis.risk_analyzer - WARNING - Insufficient data for trend: 90 < 250
2025-06-09 22:17:18,428 - analysis.risk_analyzer - WARNING - Insufficient data for option: 90 < 250
2025-06-09 22:17:18,428 - analysis.risk_analyzer - WARNING - Insufficient data for other: 90 < 250
2025-06-09 22:17:18,428 - analysis.risk_analyzer - INFO - Identifying risk concentrations...
2025-06-09 22:17:18,428 - analysis.risk_analyzer - INFO - Calculating correlation matrix...
2025-06-09 22:17:18,429 - analysis.risk_analyzer - INFO - Calculating tail risk metrics...
2025-06-09 22:17:18,431 - analysis.risk_analyzer - INFO - Calculating stress test scenarios...
2025-06-09 22:17:18,431 - analysis.risk_analyzer - INFO - Calculating risk-adjusted returns...
2025-06-09 22:17:18,431 - analysis.risk_analyzer - INFO - Validating risk metrics...
2025-06-09 22:17:18,432 - analysis.risk_analyzer - INFO - Risk metrics validation completed: 3/3 strategies passed validation
2025-06-09 22:17:18,432 - analysis.risk_analyzer - WARNING - Found 6 warnings
2025-06-09 22:17:18,432 - analysis.risk_analyzer - INFO - Adding drawdown analysis...
2025-06-09 22:17:18,432 - analysis.drawdown_analyzer - INFO - Analyzing drawdown periods...
2025-06-09 22:17:18,432 - analysis.drawdown_analyzer - INFO - Analyzing portfolio drawdowns...
2025-06-09 22:17:18,432 - analysis.drawdown_analyzer - INFO - Analyzing strategy drawdowns...
2025-06-09 22:17:18,435 - analysis.drawdown_analyzer - INFO - Analyzing time dimension drawdowns...
2025-06-09 22:17:18,435 - analysis.drawdown_analyzer - INFO - Analyzing monthly drawdowns...
2025-06-09 22:17:18,437 - analysis.drawdown_analyzer - INFO - Analyzing yearly drawdowns...
2025-06-09 22:17:18,438 - analysis.drawdown_analyzer - INFO - Generating drawdown summary...
2025-06-09 22:17:18,438 - analysis.risk_analyzer - INFO - Generating risk summary...
2025-06-09 22:17:18,438 - comprehensive_analysis - INFO - ✅ Risk validation: 3/3 strategies passed
2025-06-09 22:17:18,438 - comprehensive_analysis - WARNING - ⚠️ 6 risk warnings found
2025-06-09 22:17:18,438 - comprehensive_analysis - INFO - ✅ Risk analysis completed
2025-06-09 22:17:18,438 - comprehensive_analysis - INFO - 📊 Running contribution analysis...
2025-06-09 22:17:18,438 - analysis.contribution_analyzer - INFO - Calculating time window contributions with calendar periods...
2025-06-09 22:17:18,439 - analysis.contribution_analyzer - INFO - Time window calculation for contributions:
时间窗口计算方法: 日历期间
数据最新日期: 2025-03-31 00:00:00

时间窗口边界:
  本日 (2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本周 (2025-03-31 至 2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本月 (2025-03): 2025-03-01 至 2025-03-31 (31天)
  本季度 (Q1 2025): 2025-01-01 至 2025-03-31 (90天)
  本年 (2025): 2025-01-01 至 2025-03-31 (90天)
2025-06-09 22:17:18,439 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:17:18,440 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:17:18,440 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:17:18,441 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:17:18,441 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:17:18,442 - comprehensive_analysis - INFO - ✅ Contribution analysis completed
2025-06-09 22:17:18,442 - comprehensive_analysis - INFO - 📋 Generating comprehensive reports...
2025-06-09 22:17:18,542 - reporting.excel_reporter - INFO - Generating comprehensive Excel report...
2025-06-09 22:17:18,563 - reporting.excel_reporter - INFO - Excel report saved: reports/cta_analysis_report_20250609_221718.xlsx
2025-06-09 22:17:18,563 - comprehensive_analysis - INFO - ✅ Excel report generated: reports/cta_analysis_report_20250609_221718.xlsx
2025-06-09 22:17:19,029 - dashboard.dashboard_generator - INFO - Generating comprehensive dashboard...
2025-06-09 22:17:19,029 - dashboard.dashboard_generator - INFO - Generating static dashboard...
2025-06-09 22:17:19,594 - dashboard.dashboard_generator - INFO - Static dashboard saved: output/cta_analysis/dashboard/cta_dashboard_20250609_221719.png
2025-06-09 22:17:19,594 - dashboard.dashboard_generator - INFO - Generating interactive dashboard...
2025-06-09 22:17:19,654 - dashboard.dashboard_generator - WARNING - Failed to generate interactive dashboard: Trace type 'table' is not compatible with subplot type 'xy'
at grid position (1, 1)

See the docstring for the specs argument to plotly.subplots.make_subplots
for more information on subplot types
2025-06-09 22:17:19,654 - comprehensive_analysis - INFO - ✅ static_dashboard dashboard generated: output/cta_analysis/dashboard/cta_dashboard_20250609_221719.png
2025-06-09 22:17:19,654 - reporting.markdown_reporter - INFO - Generating comprehensive Markdown report...
2025-06-09 22:17:19,654 - reporting.markdown_reporter - INFO - Markdown report saved: reports/cta_analysis_report_20250609_221719.md
2025-06-09 22:17:19,654 - comprehensive_analysis - INFO - ✅ Markdown report generated: reports/cta_analysis_report_20250609_221719.md
2025-06-09 22:17:19,654 - comprehensive_analysis - INFO - 🔍 Validating system enhancements...
2025-06-09 22:17:19,654 - comprehensive_analysis - INFO - ✅ Calendar-based time windows validated
2025-06-09 22:17:19,654 - comprehensive_analysis - INFO - ✅ Risk metrics validation working
2025-06-09 22:17:19,654 - comprehensive_analysis - INFO - ✅ Return rate calculations validated
2025-06-09 22:17:19,654 - comprehensive_analysis - INFO - ✅ Daily performance summary in outputs
2025-06-09 22:17:19,654 - comprehensive_analysis - INFO - 📝 Generating execution summary...
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO - 🎯 COMPREHENSIVE ANALYSIS COMPLETED
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO - 📊 Data Processed:
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO -    CTA Records: 810
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO -    Position Records: 270
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO -    Date Range: 2025-01-01 00:00:00 to 2025-03-31 00:00:00
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO - 
📈 Analysis Modules:
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO -    ✅ Performance Analysis
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO -    ✅ Risk Analysis
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO -    ✅ Contribution Analysis
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO - 
📋 Outputs Generated: 3
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO -    📄 Excel Report: reports/cta_analysis_report_20250609_221718.xlsx
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO -    📄 Static_Dashboard: output/cta_analysis/dashboard/cta_dashboard_20250609_221719.png
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO -    📄 Markdown Report: reports/cta_analysis_report_20250609_221719.md
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO - 
🔍 Enhancement Validation:
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO -    ✅ Calendar Time Windows
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO -    ✅ Risk Validation
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO -    ✅ Return Rate Calculations
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO -    ✅ Daily Performance Summary
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO - 
🔧 Resolutions Applied (1):
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO -    1. Fixed 3 data quality issues
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO - 
🎉 Analysis COMPLETED SUCCESSFULLY
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO - 🔍 Validating system enhancements...
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO - ✅ Calendar-based time windows validated
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO - ✅ Risk metrics validation working
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO - ✅ Return rate calculations validated
2025-06-09 22:17:19,655 - comprehensive_analysis - INFO - ✅ Daily performance summary in outputs
2025-06-09 22:17:19,663 - comprehensive_analysis - INFO - 🔍 Loading and validating CTA data...
2025-06-09 22:17:19,663 - comprehensive_analysis - ERROR - ❌ Failed to load CTA data: [Errno 2] No such file or directory: 'processed_cta_data.csv'
2025-06-09 22:17:19,671 - comprehensive_analysis - INFO - 🚀 Starting comprehensive CTA analysis...
2025-06-09 22:17:19,671 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:17:19,671 - comprehensive_analysis - INFO - ✅ Configuration loaded successfully
2025-06-09 22:17:19,671 - comprehensive_analysis - INFO - 🔍 Loading and validating CTA data...
2025-06-09 22:17:19,672 - comprehensive_analysis - INFO - ✅ CTA data loaded with utf-8 encoding
2025-06-09 22:17:19,672 - comprehensive_analysis - INFO - 📊 CTA data shape: (810, 6)
2025-06-09 22:17:19,672 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-03-31
2025-06-09 22:17:19,672 - comprehensive_analysis - INFO - ✅ Position data loaded successfully
2025-06-09 22:17:19,672 - comprehensive_analysis - INFO - 📊 Position data shape: (270, 3)
2025-06-09 22:17:19,672 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-03-31
2025-06-09 22:17:19,672 - comprehensive_analysis - INFO - 🔍 Validating data quality...
2025-06-09 22:17:19,673 - comprehensive_analysis - INFO - ✅ CTA data dates converted to datetime
2025-06-09 22:17:19,673 - comprehensive_analysis - INFO - ✅ Position data dates converted to datetime
2025-06-09 22:17:19,674 - comprehensive_analysis - INFO - ✅ Numeric data types validated
2025-06-09 22:17:19,674 - comprehensive_analysis - INFO - ✅ Filtered CTA data from 2025-01-01 to 2025-03-31
2025-06-09 22:17:19,674 - comprehensive_analysis - INFO - ✅ Filtered position data from 2025-01-01
2025-06-09 22:17:19,674 - comprehensive_analysis - INFO - 📊 Final CTA data shape: (810, 6)
2025-06-09 22:17:19,674 - comprehensive_analysis - INFO - 📊 Final position data shape: (270, 3)
2025-06-09 22:17:19,674 - comprehensive_analysis - INFO - 📈 Running performance analysis...
2025-06-09 22:17:19,674 - analysis.performance_analyzer - INFO - Analyzing strategy category performance...
2025-06-09 22:17:19,681 - analysis.performance_analyzer - INFO - Calculating time window performance with calendar periods...
2025-06-09 22:17:19,681 - analysis.performance_analyzer - INFO - Time window calculation summary:
时间窗口计算方法: 日历期间
数据最新日期: 2025-03-31 00:00:00

时间窗口边界:
  本日 (2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本周 (2025-03-31 至 2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本月 (2025-03): 2025-03-01 至 2025-03-31 (31天)
  本季度 (Q1 2025): 2025-01-01 至 2025-03-31 (90天)
  本年 (2025): 2025-01-01 至 2025-03-31 (90天)
2025-06-09 22:17:19,687 - comprehensive_analysis - INFO - ✅ Performance analysis completed
2025-06-09 22:17:19,687 - comprehensive_analysis - INFO - ⚠️ Running risk analysis...
2025-06-09 22:17:19,687 - analysis.risk_analyzer - INFO - Running comprehensive risk analysis...
2025-06-09 22:17:19,687 - analysis.risk_analyzer - INFO - Calculating historical VaR and ES...
2025-06-09 22:17:19,687 - analysis.risk_analyzer - WARNING - Insufficient data for trend: 90 < 250
2025-06-09 22:17:19,687 - analysis.risk_analyzer - WARNING - Insufficient data for option: 90 < 250
2025-06-09 22:17:19,687 - analysis.risk_analyzer - WARNING - Insufficient data for other: 90 < 250
2025-06-09 22:17:19,687 - analysis.risk_analyzer - INFO - Identifying risk concentrations...
2025-06-09 22:17:19,687 - analysis.risk_analyzer - INFO - Calculating correlation matrix...
2025-06-09 22:17:19,687 - analysis.risk_analyzer - INFO - Calculating tail risk metrics...
2025-06-09 22:17:19,689 - analysis.risk_analyzer - INFO - Calculating stress test scenarios...
2025-06-09 22:17:19,690 - analysis.risk_analyzer - INFO - Calculating risk-adjusted returns...
2025-06-09 22:17:19,690 - analysis.risk_analyzer - INFO - Validating risk metrics...
2025-06-09 22:17:19,690 - analysis.risk_analyzer - INFO - Risk metrics validation completed: 3/3 strategies passed validation
2025-06-09 22:17:19,690 - analysis.risk_analyzer - WARNING - Found 8 warnings
2025-06-09 22:17:19,690 - analysis.risk_analyzer - INFO - Adding drawdown analysis...
2025-06-09 22:17:19,690 - analysis.drawdown_analyzer - INFO - Analyzing drawdown periods...
2025-06-09 22:17:19,690 - analysis.drawdown_analyzer - INFO - Analyzing portfolio drawdowns...
2025-06-09 22:17:19,691 - analysis.drawdown_analyzer - INFO - Analyzing strategy drawdowns...
2025-06-09 22:17:19,693 - analysis.drawdown_analyzer - INFO - Analyzing time dimension drawdowns...
2025-06-09 22:17:19,693 - analysis.drawdown_analyzer - INFO - Analyzing monthly drawdowns...
2025-06-09 22:17:19,694 - analysis.drawdown_analyzer - INFO - Analyzing yearly drawdowns...
2025-06-09 22:17:19,695 - analysis.drawdown_analyzer - INFO - Generating drawdown summary...
2025-06-09 22:17:19,695 - analysis.risk_analyzer - INFO - Generating risk summary...
2025-06-09 22:17:19,695 - comprehensive_analysis - INFO - ✅ Risk validation: 3/3 strategies passed
2025-06-09 22:17:19,695 - comprehensive_analysis - WARNING - ⚠️ 8 risk warnings found
2025-06-09 22:17:19,695 - comprehensive_analysis - INFO - ✅ Risk analysis completed
2025-06-09 22:17:19,695 - comprehensive_analysis - INFO - 📊 Running contribution analysis...
2025-06-09 22:17:19,695 - analysis.contribution_analyzer - INFO - Calculating time window contributions with calendar periods...
2025-06-09 22:17:19,695 - analysis.contribution_analyzer - INFO - Time window calculation for contributions:
时间窗口计算方法: 日历期间
数据最新日期: 2025-03-31 00:00:00

时间窗口边界:
  本日 (2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本周 (2025-03-31 至 2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本月 (2025-03): 2025-03-01 至 2025-03-31 (31天)
  本季度 (Q1 2025): 2025-01-01 至 2025-03-31 (90天)
  本年 (2025): 2025-01-01 至 2025-03-31 (90天)
2025-06-09 22:17:19,696 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:17:19,696 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:17:19,697 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:17:19,697 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:17:19,698 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:17:19,698 - comprehensive_analysis - INFO - ✅ Contribution analysis completed
2025-06-09 22:17:19,698 - comprehensive_analysis - INFO - 📋 Generating comprehensive reports...
2025-06-09 22:17:19,698 - reporting.excel_reporter - INFO - Generating comprehensive Excel report...
2025-06-09 22:17:19,718 - reporting.excel_reporter - INFO - Excel report saved: reports/cta_analysis_report_20250609_221719.xlsx
2025-06-09 22:17:19,718 - comprehensive_analysis - INFO - ✅ Excel report generated: reports/cta_analysis_report_20250609_221719.xlsx
2025-06-09 22:17:19,718 - dashboard.dashboard_generator - INFO - Generating comprehensive dashboard...
2025-06-09 22:17:19,718 - dashboard.dashboard_generator - INFO - Generating static dashboard...
2025-06-09 22:17:20,143 - dashboard.dashboard_generator - INFO - Static dashboard saved: output/cta_analysis/dashboard/cta_dashboard_20250609_221719.png
2025-06-09 22:17:20,143 - dashboard.dashboard_generator - INFO - Generating interactive dashboard...
2025-06-09 22:17:20,150 - dashboard.dashboard_generator - WARNING - Failed to generate interactive dashboard: Trace type 'table' is not compatible with subplot type 'xy'
at grid position (1, 1)

See the docstring for the specs argument to plotly.subplots.make_subplots
for more information on subplot types
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO - ✅ static_dashboard dashboard generated: output/cta_analysis/dashboard/cta_dashboard_20250609_221719.png
2025-06-09 22:17:20,151 - reporting.markdown_reporter - INFO - Generating comprehensive Markdown report...
2025-06-09 22:17:20,151 - reporting.markdown_reporter - INFO - Markdown report saved: reports/cta_analysis_report_20250609_221720.md
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO - ✅ Markdown report generated: reports/cta_analysis_report_20250609_221720.md
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO - 🔍 Validating system enhancements...
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO - ✅ Calendar-based time windows validated
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO - ✅ Risk metrics validation working
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO - ✅ Return rate calculations validated
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO - ✅ Daily performance summary in outputs
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO - 📝 Generating execution summary...
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO - 🎯 COMPREHENSIVE ANALYSIS COMPLETED
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO - 📊 Data Processed:
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO -    CTA Records: 810
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO -    Position Records: 270
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO -    Date Range: 2025-01-01 00:00:00 to 2025-03-31 00:00:00
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO - 
📈 Analysis Modules:
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO -    ✅ Performance Analysis
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO -    ✅ Risk Analysis
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO -    ✅ Contribution Analysis
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO - 
📋 Outputs Generated: 3
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO -    📄 Excel Report: reports/cta_analysis_report_20250609_221719.xlsx
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO -    📄 Static_Dashboard: output/cta_analysis/dashboard/cta_dashboard_20250609_221719.png
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO -    📄 Markdown Report: reports/cta_analysis_report_20250609_221720.md
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO - 
🔍 Enhancement Validation:
2025-06-09 22:17:20,151 - comprehensive_analysis - INFO -    ✅ Calendar Time Windows
2025-06-09 22:17:20,152 - comprehensive_analysis - INFO -    ✅ Risk Validation
2025-06-09 22:17:20,152 - comprehensive_analysis - INFO -    ✅ Return Rate Calculations
2025-06-09 22:17:20,152 - comprehensive_analysis - INFO -    ✅ Daily Performance Summary
2025-06-09 22:17:20,152 - comprehensive_analysis - INFO - 
🔧 Resolutions Applied (1):
2025-06-09 22:17:20,152 - comprehensive_analysis - INFO -    1. Fixed 3 data quality issues
2025-06-09 22:17:20,152 - comprehensive_analysis - INFO - 
🎉 Analysis COMPLETED SUCCESSFULLY
2025-06-09 22:17:20,152 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:17:20,159 - comprehensive_analysis - INFO - 🚀 Starting comprehensive CTA analysis...
2025-06-09 22:17:20,159 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:17:20,159 - comprehensive_analysis - INFO - ✅ Configuration loaded successfully
2025-06-09 22:17:20,159 - comprehensive_analysis - INFO - 🔍 Loading and validating CTA data...
2025-06-09 22:17:20,160 - comprehensive_analysis - INFO - ✅ CTA data loaded with utf-8 encoding
2025-06-09 22:17:20,160 - comprehensive_analysis - INFO - 📊 CTA data shape: (810, 6)
2025-06-09 22:17:20,160 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-03-31
2025-06-09 22:17:20,161 - comprehensive_analysis - INFO - ✅ Position data loaded successfully
2025-06-09 22:17:20,161 - comprehensive_analysis - INFO - 📊 Position data shape: (270, 3)
2025-06-09 22:17:20,161 - comprehensive_analysis - INFO - 📅 Date range: 2025-01-01 to 2025-03-31
2025-06-09 22:17:20,161 - comprehensive_analysis - INFO - 🔍 Validating data quality...
2025-06-09 22:17:20,161 - comprehensive_analysis - INFO - ✅ CTA data dates converted to datetime
2025-06-09 22:17:20,161 - comprehensive_analysis - INFO - ✅ Position data dates converted to datetime
2025-06-09 22:17:20,162 - comprehensive_analysis - INFO - ✅ Numeric data types validated
2025-06-09 22:17:20,162 - comprehensive_analysis - INFO - ✅ Filtered CTA data from 2025-01-01 to 2025-03-31
2025-06-09 22:17:20,163 - comprehensive_analysis - INFO - ✅ Filtered position data from 2025-01-01
2025-06-09 22:17:20,163 - comprehensive_analysis - INFO - 📊 Final CTA data shape: (810, 6)
2025-06-09 22:17:20,163 - comprehensive_analysis - INFO - 📊 Final position data shape: (270, 3)
2025-06-09 22:17:20,163 - comprehensive_analysis - INFO - 📈 Running performance analysis...
2025-06-09 22:17:20,163 - analysis.performance_analyzer - INFO - Analyzing strategy category performance...
2025-06-09 22:17:20,168 - analysis.performance_analyzer - WARNING - Found 1 outlier returns, capping them
2025-06-09 22:17:20,169 - analysis.performance_analyzer - INFO - Calculating time window performance with calendar periods...
2025-06-09 22:17:20,170 - analysis.performance_analyzer - INFO - Time window calculation summary:
时间窗口计算方法: 日历期间
数据最新日期: 2025-03-31 00:00:00

时间窗口边界:
  本日 (2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本周 (2025-03-31 至 2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本月 (2025-03): 2025-03-01 至 2025-03-31 (31天)
  本季度 (Q1 2025): 2025-01-01 至 2025-03-31 (90天)
  本年 (2025): 2025-01-01 至 2025-03-31 (90天)
2025-06-09 22:17:20,175 - comprehensive_analysis - INFO - ✅ Performance analysis completed
2025-06-09 22:17:20,175 - comprehensive_analysis - INFO - ⚠️ Running risk analysis...
2025-06-09 22:17:20,175 - analysis.risk_analyzer - INFO - Running comprehensive risk analysis...
2025-06-09 22:17:20,175 - analysis.risk_analyzer - INFO - Calculating historical VaR and ES...
2025-06-09 22:17:20,175 - analysis.risk_analyzer - WARNING - Insufficient data for trend: 90 < 250
2025-06-09 22:17:20,175 - analysis.risk_analyzer - WARNING - Insufficient data for option: 90 < 250
2025-06-09 22:17:20,175 - analysis.risk_analyzer - WARNING - Insufficient data for other: 90 < 250
2025-06-09 22:17:20,175 - analysis.risk_analyzer - INFO - Identifying risk concentrations...
2025-06-09 22:17:20,175 - analysis.risk_analyzer - INFO - Calculating correlation matrix...
2025-06-09 22:17:20,175 - analysis.risk_analyzer - INFO - Calculating tail risk metrics...
2025-06-09 22:17:20,177 - analysis.risk_analyzer - INFO - Calculating stress test scenarios...
2025-06-09 22:17:20,178 - analysis.risk_analyzer - INFO - Calculating risk-adjusted returns...
2025-06-09 22:17:20,178 - analysis.risk_analyzer - INFO - Validating risk metrics...
2025-06-09 22:17:20,178 - analysis.risk_analyzer - INFO - Risk metrics validation completed: 3/3 strategies passed validation
2025-06-09 22:17:20,178 - analysis.risk_analyzer - WARNING - Found 8 warnings
2025-06-09 22:17:20,178 - analysis.risk_analyzer - INFO - Adding drawdown analysis...
2025-06-09 22:17:20,178 - analysis.drawdown_analyzer - INFO - Analyzing drawdown periods...
2025-06-09 22:17:20,178 - analysis.drawdown_analyzer - INFO - Analyzing portfolio drawdowns...
2025-06-09 22:17:20,178 - analysis.drawdown_analyzer - INFO - Analyzing strategy drawdowns...
2025-06-09 22:17:20,181 - analysis.drawdown_analyzer - INFO - Analyzing time dimension drawdowns...
2025-06-09 22:17:20,181 - analysis.drawdown_analyzer - INFO - Analyzing monthly drawdowns...
2025-06-09 22:17:20,181 - analysis.drawdown_analyzer - INFO - Analyzing yearly drawdowns...
2025-06-09 22:17:20,182 - analysis.drawdown_analyzer - INFO - Generating drawdown summary...
2025-06-09 22:17:20,182 - analysis.risk_analyzer - INFO - Generating risk summary...
2025-06-09 22:17:20,182 - comprehensive_analysis - INFO - ✅ Risk validation: 3/3 strategies passed
2025-06-09 22:17:20,182 - comprehensive_analysis - WARNING - ⚠️ 8 risk warnings found
2025-06-09 22:17:20,182 - comprehensive_analysis - INFO - ✅ Risk analysis completed
2025-06-09 22:17:20,182 - comprehensive_analysis - INFO - 📊 Running contribution analysis...
2025-06-09 22:17:20,182 - analysis.contribution_analyzer - INFO - Calculating time window contributions with calendar periods...
2025-06-09 22:17:20,183 - analysis.contribution_analyzer - INFO - Time window calculation for contributions:
时间窗口计算方法: 日历期间
数据最新日期: 2025-03-31 00:00:00

时间窗口边界:
  本日 (2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本周 (2025-03-31 至 2025-03-31): 2025-03-31 至 2025-03-31 (1天)
  本月 (2025-03): 2025-03-01 至 2025-03-31 (31天)
  本季度 (Q1 2025): 2025-01-01 至 2025-03-31 (90天)
  本年 (2025): 2025-01-01 至 2025-03-31 (90天)
2025-06-09 22:17:20,183 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:17:20,184 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:17:20,184 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:17:20,185 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:17:20,185 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-09 22:17:20,186 - comprehensive_analysis - INFO - ✅ Contribution analysis completed
2025-06-09 22:17:20,186 - comprehensive_analysis - INFO - 📋 Generating comprehensive reports...
2025-06-09 22:17:20,186 - reporting.excel_reporter - INFO - Generating comprehensive Excel report...
2025-06-09 22:17:20,204 - reporting.excel_reporter - INFO - Excel report saved: reports/cta_analysis_report_20250609_221720.xlsx
2025-06-09 22:17:20,204 - comprehensive_analysis - INFO - ✅ Excel report generated: reports/cta_analysis_report_20250609_221720.xlsx
2025-06-09 22:17:20,205 - dashboard.dashboard_generator - INFO - Generating comprehensive dashboard...
2025-06-09 22:17:20,205 - dashboard.dashboard_generator - INFO - Generating static dashboard...
2025-06-09 22:17:20,653 - dashboard.dashboard_generator - INFO - Static dashboard saved: output/cta_analysis/dashboard/cta_dashboard_20250609_221720.png
2025-06-09 22:17:20,653 - dashboard.dashboard_generator - INFO - Generating interactive dashboard...
2025-06-09 22:17:20,660 - dashboard.dashboard_generator - WARNING - Failed to generate interactive dashboard: Trace type 'table' is not compatible with subplot type 'xy'
at grid position (1, 1)

See the docstring for the specs argument to plotly.subplots.make_subplots
for more information on subplot types
2025-06-09 22:17:20,660 - comprehensive_analysis - INFO - ✅ static_dashboard dashboard generated: output/cta_analysis/dashboard/cta_dashboard_20250609_221720.png
2025-06-09 22:17:20,660 - reporting.markdown_reporter - INFO - Generating comprehensive Markdown report...
2025-06-09 22:17:20,661 - reporting.markdown_reporter - INFO - Markdown report saved: reports/cta_analysis_report_20250609_221720.md
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - ✅ Markdown report generated: reports/cta_analysis_report_20250609_221720.md
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - 🔍 Validating system enhancements...
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - ✅ Calendar-based time windows validated
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - ✅ Risk metrics validation working
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - ✅ Return rate calculations validated
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - ✅ Daily performance summary in outputs
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - 📝 Generating execution summary...
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - 🎯 COMPREHENSIVE ANALYSIS COMPLETED
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - 📊 Data Processed:
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO -    CTA Records: 810
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO -    Position Records: 270
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO -    Date Range: 2025-01-01 00:00:00 to 2025-03-31 00:00:00
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - 
📈 Analysis Modules:
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO -    ✅ Performance Analysis
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO -    ✅ Risk Analysis
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO -    ✅ Contribution Analysis
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - 
📋 Outputs Generated: 3
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO -    📄 Excel Report: reports/cta_analysis_report_20250609_221720.xlsx
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO -    📄 Static_Dashboard: output/cta_analysis/dashboard/cta_dashboard_20250609_221720.png
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO -    📄 Markdown Report: reports/cta_analysis_report_20250609_221720.md
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - 
🔍 Enhancement Validation:
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO -    ✅ Calendar Time Windows
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO -    ✅ Risk Validation
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO -    ✅ Return Rate Calculations
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO -    ✅ Daily Performance Summary
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - 
🔧 Resolutions Applied (1):
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO -    1. Fixed 3 data quality issues
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - 
🎉 Analysis COMPLETED SUCCESSFULLY
2025-06-09 22:17:20,661 - comprehensive_analysis - INFO - ================================================================================
2025-06-09 22:17:20,663 - comprehensive_analysis - INFO - ✅ Configuration loaded successfully
2025-06-11 10:18:03,371 - __main__ - INFO - ============================================================
2025-06-11 10:18:03,388 - comprehensive_analysis - INFO - ================================================================================
2025-06-11 10:18:04,149 - __main__ - ERROR - ���ֵ�����:
2025-06-11 10:18:04,149 - __main__ - ERROR -    1. Position data loading error: '<=' not supported between instances of 'str' and 'float'
2025-06-11 14:44:19,878 - __main__ - INFO - 🚀 启动CTA策略分析系统
2025-06-11 14:44:19,878 - __main__ - INFO - ============================================================
2025-06-11 14:44:19,878 - __main__ - INFO - 📊 运行完整CTA策略分析...
2025-06-11 14:44:19,878 - comprehensive_analysis - INFO - 🚀 Starting comprehensive CTA analysis...
2025-06-11 14:44:19,878 - comprehensive_analysis - INFO - ================================================================================
2025-06-11 14:44:19,916 - comprehensive_analysis - INFO - ✅ Configuration loaded successfully
2025-06-11 14:44:19,916 - comprehensive_analysis - INFO - 🔍 Loading and validating CTA data...
2025-06-11 14:44:20,199 - comprehensive_analysis - INFO - ✅ CTA data loaded with gbk encoding
2025-06-11 14:44:20,199 - comprehensive_analysis - INFO - 📊 CTA data shape: (113070, 26)
2025-06-11 14:44:20,205 - comprehensive_analysis - INFO - 📅 Date range: 2022/10/10 to 2025/5/9
2025-06-11 14:44:20,206 - comprehensive_analysis - INFO - ✅ Position data loaded successfully
2025-06-11 14:44:20,206 - comprehensive_analysis - INFO - 📊 Position data shape: (350, 8)
2025-06-11 14:44:20,206 - comprehensive_analysis - ERROR - ❌ Failed to load position data: '<=' not supported between instances of 'str' and 'float'
2025-06-11 14:44:20,206 - __main__ - ERROR - ❌ 分析过程中发生错误
2025-06-11 14:44:20,206 - __main__ - ERROR - 发现的问题:
2025-06-11 14:44:20,206 - __main__ - ERROR -    1. Position data loading error: '<=' not supported between instances of 'str' and 'float'
2025-06-11 14:47:21,673 - __main__ - INFO - 🚀 启动CTA策略分析系统
2025-06-11 14:47:21,673 - __main__ - INFO - ============================================================
2025-06-11 14:47:21,673 - __main__ - INFO - 🔍 验证系统配置和数据...
2025-06-11 14:47:28,873 - __main__ - INFO - 🚀 启动CTA策略分析系统
2025-06-11 14:47:28,874 - __main__ - INFO - ============================================================
2025-06-11 14:47:28,874 - __main__ - INFO - 📊 运行完整CTA策略分析...
2025-06-11 14:47:28,874 - comprehensive_analysis - INFO - 🚀 Starting comprehensive CTA analysis...
2025-06-11 14:47:28,874 - comprehensive_analysis - INFO - ================================================================================
2025-06-11 14:47:28,894 - comprehensive_analysis - INFO - ✅ Configuration loaded successfully
2025-06-11 14:47:28,894 - comprehensive_analysis - INFO - 🔍 Loading and validating CTA data...
2025-06-11 14:47:29,195 - comprehensive_analysis - INFO - ✅ CTA data loaded with gbk encoding
2025-06-11 14:47:29,196 - comprehensive_analysis - INFO - 📊 CTA data shape: (114900, 26)
2025-06-11 14:47:29,202 - comprehensive_analysis - INFO - 📅 Date range: 2022-07-27 to 2025-06-06
2025-06-11 14:47:29,204 - comprehensive_analysis - INFO - ✅ Position data loaded successfully
2025-06-11 14:47:29,204 - comprehensive_analysis - INFO - 📊 Position data shape: (350, 8)
2025-06-11 14:47:29,204 - comprehensive_analysis - ERROR - ❌ Failed to load position data: '<=' not supported between instances of 'str' and 'float'
2025-06-11 14:47:29,204 - __main__ - ERROR - ❌ 分析过程中发生错误
2025-06-11 14:47:29,204 - __main__ - ERROR - 发现的问题:
2025-06-11 14:47:29,204 - __main__ - ERROR -    1. Position data loading error: '<=' not supported between instances of 'str' and 'float'
2025-06-11 14:48:05,963 - __main__ - INFO - 🚀 启动CTA策略分析系统
2025-06-11 14:48:05,963 - __main__ - INFO - ============================================================
2025-06-11 14:48:05,963 - __main__ - INFO - 📊 运行完整CTA策略分析...
2025-06-11 14:48:05,963 - comprehensive_analysis - INFO - 🚀 Starting comprehensive CTA analysis...
2025-06-11 14:48:05,963 - comprehensive_analysis - INFO - ================================================================================
2025-06-11 14:48:05,983 - comprehensive_analysis - INFO - ✅ Configuration loaded successfully
2025-06-11 14:48:05,983 - comprehensive_analysis - INFO - 🔍 Loading and validating CTA data...
2025-06-11 14:48:06,280 - comprehensive_analysis - INFO - ✅ CTA data loaded with gbk encoding
2025-06-11 14:48:06,280 - comprehensive_analysis - INFO - 📊 CTA data shape: (114900, 26)
2025-06-11 14:48:06,286 - comprehensive_analysis - INFO - 📅 Date range: 2022-07-27 to 2025-06-06
2025-06-11 14:48:06,289 - comprehensive_analysis - INFO - ✅ Position data loaded successfully
2025-06-11 14:48:06,289 - comprehensive_analysis - INFO - 📊 Position data shape: (350, 8)
2025-06-11 14:48:06,289 - comprehensive_analysis - ERROR - ❌ Failed to load position data: '<=' not supported between instances of 'str' and 'float'
2025-06-11 14:48:06,289 - __main__ - ERROR - ❌ 分析过程中发生错误
2025-06-11 14:48:06,289 - __main__ - ERROR - 发现的问题:
2025-06-11 14:48:06,289 - __main__ - ERROR -    1. Position data loading error: '<=' not supported between instances of 'str' and 'float'
2025-06-11 14:48:19,454 - __main__ - INFO - 🚀 启动CTA策略分析系统
2025-06-11 14:48:19,454 - __main__ - INFO - ============================================================
2025-06-11 14:48:19,454 - __main__ - INFO - 📊 运行完整CTA策略分析...
2025-06-11 14:48:19,454 - comprehensive_analysis - INFO - 🚀 Starting comprehensive CTA analysis...
2025-06-11 14:48:19,454 - comprehensive_analysis - INFO - ================================================================================
2025-06-11 14:48:19,474 - comprehensive_analysis - INFO - ✅ Configuration loaded successfully
2025-06-11 14:48:19,474 - comprehensive_analysis - INFO - 🔍 Loading and validating CTA data...
2025-06-11 14:48:19,769 - comprehensive_analysis - INFO - ✅ CTA data loaded with gbk encoding
2025-06-11 14:48:19,770 - comprehensive_analysis - INFO - 📊 CTA data shape: (114900, 26)
2025-06-11 14:48:19,775 - comprehensive_analysis - INFO - 📅 Date range: 2022-07-27 to 2025-06-06
2025-06-11 14:48:19,777 - comprehensive_analysis - INFO - ✅ Position data loaded successfully
2025-06-11 14:48:19,777 - comprehensive_analysis - INFO - 📊 Position data shape: (350, 8)
2025-06-11 14:48:19,777 - comprehensive_analysis - ERROR - ❌ Failed to load position data: '<=' not supported between instances of 'str' and 'float'
2025-06-11 14:48:19,781 - comprehensive_analysis - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Library/CloudStorage/OneDrive-个人/工作/Python测试数据/cta_analysis_system/comprehensive_analysis.py", line 100, in load_and_validate_data
    logger.info(f"📅 Date range: {position_data['date'].min()} to {position_data['date'].max()}")
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.11/site-packages/pandas/core/series.py", line 6507, in min
    return NDFrame.min(self, axis, skipna, numeric_only, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.11/site-packages/pandas/core/generic.py", line 12388, in min
    return self._stat_function(
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.11/site-packages/pandas/core/generic.py", line 12377, in _stat_function
    return self._reduce(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.11/site-packages/pandas/core/series.py", line 6457, in _reduce
    return op(delegate, skipna=skipna, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.11/site-packages/pandas/core/nanops.py", line 147, in f
    result = alt(values, axis=axis, skipna=skipna, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.11/site-packages/pandas/core/nanops.py", line 404, in new_func
    result = func(values, axis=axis, skipna=skipna, mask=mask, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.11/site-packages/pandas/core/nanops.py", line 1098, in reduction
    result = getattr(values, meth)(axis)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.11/site-packages/numpy/core/_methods.py", line 45, in _amin
    return umr_minimum(a, axis, None, out, keepdims, initial, where)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: '<=' not supported between instances of 'str' and 'float'

2025-06-11 14:48:19,781 - __main__ - ERROR - ❌ 分析过程中发生错误
2025-06-11 14:48:19,781 - __main__ - ERROR - 发现的问题:
2025-06-11 14:48:19,782 - __main__ - ERROR -    1. Position data loading error: '<=' not supported between instances of 'str' and 'float'
2025-06-11 14:48:40,162 - __main__ - INFO - 🚀 启动CTA策略分析系统
2025-06-11 14:48:40,162 - __main__ - INFO - ============================================================
2025-06-11 14:48:40,162 - __main__ - INFO - 📊 运行完整CTA策略分析...
2025-06-11 14:48:40,162 - comprehensive_analysis - INFO - 🚀 Starting comprehensive CTA analysis...
2025-06-11 14:48:40,162 - comprehensive_analysis - INFO - ================================================================================
2025-06-11 14:48:40,184 - comprehensive_analysis - INFO - ✅ Configuration loaded successfully
2025-06-11 14:48:40,184 - comprehensive_analysis - INFO - 🔍 Loading and validating CTA data...
2025-06-11 14:48:40,481 - comprehensive_analysis - INFO - ✅ CTA data loaded with gbk encoding
2025-06-11 14:48:40,481 - comprehensive_analysis - INFO - 📊 CTA data shape: (114900, 26)
2025-06-11 14:48:40,487 - comprehensive_analysis - INFO - 📅 Date range: 2022-07-27 to 2025-06-06
2025-06-11 14:48:40,491 - comprehensive_analysis - INFO - ✅ Position data loaded successfully
2025-06-11 14:48:40,491 - comprehensive_analysis - INFO - 📊 Position data shape: (345, 8)
2025-06-11 14:48:40,491 - comprehensive_analysis - INFO - 📅 Date range: 2024-01-02 00:00:00 to 2025-06-10 00:00:00
2025-06-11 14:48:40,491 - comprehensive_analysis - INFO - 🔍 Validating data quality...
2025-06-11 14:48:40,496 - comprehensive_analysis - INFO - ✅ CTA data dates converted to datetime
2025-06-11 14:48:40,591 - comprehensive_analysis - INFO - ✅ Filled 84593 missing values in CTA data
2025-06-11 14:48:40,605 - comprehensive_analysis - INFO - ✅ Numeric data types validated
2025-06-11 14:48:40,611 - comprehensive_analysis - INFO - ✅ Filtered CTA data from 2025-01-01 to 2025-06-06
2025-06-11 14:48:40,611 - comprehensive_analysis - INFO - ✅ Filtered position data from 2025-01-01
2025-06-11 14:48:40,611 - comprehensive_analysis - INFO - 📊 Final CTA data shape: (22905, 26)
2025-06-11 14:48:40,611 - comprehensive_analysis - INFO - 📊 Final position data shape: (103, 8)
2025-06-11 14:48:40,615 - comprehensive_analysis - INFO - 📈 Running performance analysis...
2025-06-11 14:48:41,052 - analysis.performance_analyzer - INFO - Analyzing strategy category performance...
2025-06-11 14:48:41,056 - analysis.performance_analyzer - WARNING - Found 2 outlier returns, capping them
2025-06-11 14:48:41,063 - analysis.performance_analyzer - WARNING - Found 2 outlier returns, capping them
2025-06-11 14:48:41,067 - analysis.performance_analyzer - WARNING - Found 1 outlier returns, capping them
2025-06-11 14:48:41,070 - analysis.performance_analyzer - INFO - Calculating time window performance with calendar periods...
2025-06-11 14:48:41,071 - analysis.performance_analyzer - INFO - Time window calculation summary:
时间窗口计算方法: 日历期间
数据最新日期: 2025-06-06 00:00:00

时间窗口边界:
  本日 (2025-06-06): 2025-06-06 至 2025-06-06 (1天)
  本周 (2025-06-02 至 2025-06-06): 2025-06-02 至 2025-06-06 (5天)
  本月 (2025-06): 2025-06-01 至 2025-06-06 (6天)
  本季度 (Q2 2025): 2025-04-01 至 2025-06-06 (67天)
  本年 (2025): 2025-01-01 至 2025-06-06 (157天)
2025-06-11 14:48:41,092 - comprehensive_analysis - INFO - ✅ Performance analysis completed
2025-06-11 14:48:41,092 - comprehensive_analysis - INFO - ⚠️ Running risk analysis...
2025-06-11 14:48:41,092 - analysis.risk_analyzer - INFO - Running comprehensive risk analysis...
2025-06-11 14:48:41,092 - analysis.risk_analyzer - INFO - Calculating historical VaR and ES...
2025-06-11 14:48:41,092 - analysis.risk_analyzer - WARNING - Insufficient data for trend: 103 < 250
2025-06-11 14:48:41,092 - analysis.risk_analyzer - WARNING - Insufficient data for 0: 103 < 250
2025-06-11 14:48:41,092 - analysis.risk_analyzer - WARNING - Insufficient data for other: 103 < 250
2025-06-11 14:48:41,092 - analysis.risk_analyzer - WARNING - Insufficient data for option: 103 < 250
2025-06-11 14:48:41,092 - analysis.risk_analyzer - WARNING - Insufficient data for orderflow: 103 < 250
2025-06-11 14:48:41,092 - analysis.risk_analyzer - INFO - Identifying risk concentrations...
2025-06-11 14:48:41,092 - analysis.risk_analyzer - INFO - Calculating correlation matrix...
2025-06-11 14:48:41,093 - analysis.risk_analyzer - INFO - Calculating tail risk metrics...
2025-06-11 14:48:41,096 - analysis.risk_analyzer - INFO - Calculating stress test scenarios...
2025-06-11 14:48:41,098 - analysis.risk_analyzer - INFO - Calculating risk-adjusted returns...
2025-06-11 14:48:41,098 - analysis.risk_analyzer - INFO - Validating risk metrics...
2025-06-11 14:48:41,098 - analysis.risk_analyzer - INFO - Risk metrics validation completed: 5/5 strategies passed validation
2025-06-11 14:48:41,098 - analysis.risk_analyzer - WARNING - Found 11 warnings
2025-06-11 14:48:41,098 - analysis.risk_analyzer - INFO - Adding drawdown analysis...
2025-06-11 14:48:41,098 - analysis.drawdown_analyzer - INFO - Analyzing drawdown periods...
2025-06-11 14:48:41,098 - analysis.drawdown_analyzer - INFO - Analyzing portfolio drawdowns...
2025-06-11 14:48:41,098 - analysis.drawdown_analyzer - INFO - Analyzing strategy drawdowns...
2025-06-11 14:48:41,100 - analysis.drawdown_analyzer - INFO - Analyzing time dimension drawdowns...
2025-06-11 14:48:41,100 - analysis.drawdown_analyzer - INFO - Analyzing monthly drawdowns...
2025-06-11 14:48:41,102 - analysis.drawdown_analyzer - INFO - Analyzing yearly drawdowns...
2025-06-11 14:48:41,102 - analysis.drawdown_analyzer - INFO - Generating drawdown summary...
2025-06-11 14:48:41,102 - analysis.risk_analyzer - INFO - Generating risk summary...
2025-06-11 14:48:41,102 - comprehensive_analysis - INFO - ✅ Risk validation: 5/5 strategies passed
2025-06-11 14:48:41,102 - comprehensive_analysis - WARNING - ⚠️ 11 risk warnings found
2025-06-11 14:48:41,102 - comprehensive_analysis - INFO - ✅ Risk analysis completed
2025-06-11 14:48:41,102 - comprehensive_analysis - INFO - 📊 Running contribution analysis...
2025-06-11 14:48:41,102 - analysis.contribution_analyzer - INFO - Calculating time window contributions with calendar periods...
2025-06-11 14:48:41,103 - analysis.contribution_analyzer - INFO - Time window calculation for contributions:
时间窗口计算方法: 日历期间
数据最新日期: 2025-06-06 00:00:00

时间窗口边界:
  本日 (2025-06-06): 2025-06-06 至 2025-06-06 (1天)
  本周 (2025-06-02 至 2025-06-06): 2025-06-02 至 2025-06-06 (5天)
  本月 (2025-06): 2025-06-01 至 2025-06-06 (6天)
  本季度 (Q2 2025): 2025-04-01 至 2025-06-06 (67天)
  本年 (2025): 2025-01-01 至 2025-06-06 (157天)
2025-06-11 14:48:41,103 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-11 14:48:41,104 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-11 14:48:41,106 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-11 14:48:41,108 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-11 14:48:41,112 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-11 14:48:41,115 - comprehensive_analysis - INFO - ✅ Contribution analysis completed
2025-06-11 14:48:41,115 - comprehensive_analysis - INFO - 📋 Generating comprehensive reports...
2025-06-11 14:48:41,212 - reporting.excel_reporter - INFO - Generating comprehensive Excel report...
2025-06-11 14:48:41,233 - reporting.excel_reporter - INFO - Excel report saved: reports/cta_analysis_report_20250611_144841.xlsx
2025-06-11 14:48:41,233 - comprehensive_analysis - INFO - ✅ Excel report generated: reports/cta_analysis_report_20250611_144841.xlsx
2025-06-11 14:48:41,770 - dashboard.dashboard_generator - INFO - Generating comprehensive dashboard...
2025-06-11 14:48:41,770 - dashboard.dashboard_generator - INFO - Generating static dashboard...
2025-06-11 14:48:42,435 - dashboard.dashboard_generator - INFO - Static dashboard saved: output/cta_analysis/dashboard/cta_dashboard_20250611_144841.png
2025-06-11 14:48:42,435 - dashboard.dashboard_generator - INFO - Generating interactive dashboard...
2025-06-11 14:48:42,504 - dashboard.dashboard_generator - WARNING - Failed to generate interactive dashboard: Trace type 'table' is not compatible with subplot type 'xy'
at grid position (1, 1)

See the docstring for the specs argument to plotly.subplots.make_subplots
for more information on subplot types
2025-06-11 14:48:42,504 - comprehensive_analysis - INFO - ✅ static_dashboard dashboard generated: output/cta_analysis/dashboard/cta_dashboard_20250611_144841.png
2025-06-11 14:48:42,504 - reporting.markdown_reporter - INFO - Generating comprehensive Markdown report...
2025-06-11 14:48:42,505 - reporting.markdown_reporter - INFO - Markdown report saved: reports/cta_analysis_report_20250611_144842.md
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO - ✅ Markdown report generated: reports/cta_analysis_report_20250611_144842.md
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO - 🔍 Validating system enhancements...
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO - ✅ Calendar-based time windows validated
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO - ✅ Risk metrics validation working
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO - ✅ Return rate calculations validated
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO - ✅ Daily performance summary in outputs
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO - 📝 Generating execution summary...
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO - ================================================================================
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO - 🎯 COMPREHENSIVE ANALYSIS COMPLETED
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO - ================================================================================
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO - 📊 Data Processed:
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO -    CTA Records: 22,905
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO -    Position Records: 103
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO -    Date Range: 2025-01-02 00:00:00 to 2025-06-06 00:00:00
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO - 
📈 Analysis Modules:
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO -    ✅ Performance Analysis
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO -    ✅ Risk Analysis
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO -    ✅ Contribution Analysis
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO - 
📋 Outputs Generated: 3
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO -    📄 Excel Report: reports/cta_analysis_report_20250611_144841.xlsx
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO -    📄 Static_Dashboard: output/cta_analysis/dashboard/cta_dashboard_20250611_144841.png
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO -    📄 Markdown Report: reports/cta_analysis_report_20250611_144842.md
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO - 
🔍 Enhancement Validation:
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO -    ✅ Calendar Time Windows
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO -    ✅ Risk Validation
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO -    ✅ Return Rate Calculations
2025-06-11 14:48:42,505 - comprehensive_analysis - INFO -    ✅ Daily Performance Summary
2025-06-11 14:48:42,506 - comprehensive_analysis - INFO - 
🔧 Resolutions Applied (1):
2025-06-11 14:48:42,506 - comprehensive_analysis - INFO -    1. Fixed 3 data quality issues
2025-06-11 14:48:42,506 - comprehensive_analysis - INFO - 
🎉 Analysis COMPLETED SUCCESSFULLY
2025-06-11 14:48:42,506 - comprehensive_analysis - INFO - ================================================================================
2025-06-11 14:48:42,506 - __main__ - INFO - ✅ 分析完成成功！
2025-06-11 14:48:42,506 - __main__ - INFO - 
📋 生成的报告文件 (3个):
2025-06-11 14:48:42,506 - __main__ - INFO -    📄 Excel Report: reports/cta_analysis_report_20250611_144841.xlsx
2025-06-11 14:48:42,506 - __main__ - INFO -    📄 Static_Dashboard: output/cta_analysis/dashboard/cta_dashboard_20250611_144841.png
2025-06-11 14:48:42,506 - __main__ - INFO -    📄 Markdown Report: reports/cta_analysis_report_20250611_144842.md
2025-06-11 14:48:42,506 - comprehensive_analysis - INFO - 📝 Generating execution summary...
2025-06-11 14:48:42,506 - __main__ - INFO - 
📊 分析摘要:
2025-06-11 14:48:42,506 - __main__ - INFO -    处理记录数: 22,905
2025-06-11 14:48:42,506 - __main__ - INFO -    分析时间范围: 2025-01-02 00:00:00 to 2025-06-06 00:00:00
2025-06-11 14:48:42,506 - __main__ - INFO -    发现问题数: 0
2025-06-11 14:48:42,506 - __main__ - INFO -    解决方案数: 1
2025-06-11 14:48:42,506 - __main__ - INFO - 
🎉 CTA策略分析完成！请查看生成的报告文件。
2025-06-11 15:01:22,555 - __main__ - INFO - ============================================================
2025-06-11 15:01:22,557 - comprehensive_analysis - INFO - ================================================================================
2025-06-11 15:01:24,344 - analysis.performance_analyzer - INFO - Analyzing strategy category performance...
2025-06-11 15:01:24,355 - analysis.performance_analyzer - WARNING - Found 2 outlier returns, capping them
2025-06-11 15:01:24,376 - analysis.performance_analyzer - WARNING - Found 2 outlier returns, capping them
2025-06-11 15:01:24,389 - analysis.performance_analyzer - WARNING - Found 1 outlier returns, capping them
2025-06-11 15:01:24,404 - analysis.performance_analyzer - INFO - Calculating time window performance with calendar periods...
2025-06-11 15:01:24,405 - analysis.performance_analyzer - INFO - Time window calculation summary:
ʱ�䴰�ڼ��㷽��: �����ڼ�
������������: 2025-06-06 00:00:00

ʱ�䴰�ڱ߽�:
  ���� (2025-06-06): 2025-06-06 �� 2025-06-06 (1��)
  ���� (2025-06-02 �� 2025-06-06): 2025-06-02 �� 2025-06-06 (5��)
  ���� (2025-06): 2025-06-01 �� 2025-06-06 (6��)
  ������ (Q2 2025): 2025-04-01 �� 2025-06-06 (67��)
  ���� (2025): 2025-01-01 �� 2025-06-06 (157��)
2025-06-11 15:01:24,469 - analysis.risk_analyzer - INFO - Running comprehensive risk analysis...
2025-06-11 15:01:24,469 - analysis.risk_analyzer - INFO - Calculating historical VaR and ES...
2025-06-11 15:01:24,469 - analysis.risk_analyzer - WARNING - Insufficient data for trend: 103 < 250
2025-06-11 15:01:24,470 - analysis.risk_analyzer - WARNING - Insufficient data for 0: 103 < 250
2025-06-11 15:01:24,470 - analysis.risk_analyzer - WARNING - Insufficient data for other: 103 < 250
2025-06-11 15:01:24,470 - analysis.risk_analyzer - WARNING - Insufficient data for option: 103 < 250
2025-06-11 15:01:24,471 - analysis.risk_analyzer - WARNING - Insufficient data for orderflow: 103 < 250
2025-06-11 15:01:24,471 - analysis.risk_analyzer - INFO - Identifying risk concentrations...
2025-06-11 15:01:24,471 - analysis.risk_analyzer - INFO - Calculating correlation matrix...
2025-06-11 15:01:24,475 - analysis.risk_analyzer - INFO - Calculating tail risk metrics...
2025-06-11 15:01:24,487 - analysis.risk_analyzer - INFO - Calculating stress test scenarios...
2025-06-11 15:01:24,492 - analysis.risk_analyzer - INFO - Calculating risk-adjusted returns...
2025-06-11 15:01:24,493 - analysis.risk_analyzer - INFO - Validating risk metrics...
2025-06-11 15:01:24,493 - analysis.risk_analyzer - INFO - Risk metrics validation completed: 5/5 strategies passed validation
2025-06-11 15:01:24,494 - analysis.risk_analyzer - WARNING - Found 11 warnings
2025-06-11 15:01:24,494 - analysis.risk_analyzer - INFO - Adding drawdown analysis...
2025-06-11 15:01:24,494 - analysis.drawdown_analyzer - INFO - Analyzing drawdown periods...
2025-06-11 15:01:24,494 - analysis.drawdown_analyzer - INFO - Analyzing portfolio drawdowns...
2025-06-11 15:01:24,494 - analysis.drawdown_analyzer - INFO - Analyzing strategy drawdowns...
2025-06-11 15:01:24,504 - analysis.drawdown_analyzer - INFO - Analyzing time dimension drawdowns...
2025-06-11 15:01:24,504 - analysis.drawdown_analyzer - INFO - Analyzing monthly drawdowns...
2025-06-11 15:01:24,509 - analysis.drawdown_analyzer - INFO - Analyzing yearly drawdowns...
2025-06-11 15:01:24,511 - analysis.drawdown_analyzer - INFO - Generating drawdown summary...
2025-06-11 15:01:24,512 - analysis.risk_analyzer - INFO - Generating risk summary...
2025-06-11 15:01:24,518 - analysis.contribution_analyzer - INFO - Calculating time window contributions with calendar periods...
2025-06-11 15:01:24,519 - analysis.contribution_analyzer - INFO - Time window calculation for contributions:
ʱ�䴰�ڼ��㷽��: �����ڼ�
������������: 2025-06-06 00:00:00

ʱ�䴰�ڱ߽�:
  ���� (2025-06-06): 2025-06-06 �� 2025-06-06 (1��)
  ���� (2025-06-02 �� 2025-06-06): 2025-06-02 �� 2025-06-06 (5��)
  ���� (2025-06): 2025-06-01 �� 2025-06-06 (6��)
  ������ (Q2 2025): 2025-04-01 �� 2025-06-06 (67��)
  ���� (2025): 2025-01-01 �� 2025-06-06 (157��)
2025-06-11 15:01:24,521 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-11 15:01:24,524 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-11 15:01:24,526 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-11 15:01:24,532 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-11 15:01:24,541 - analysis.contribution_analyzer - INFO - Calculating strategy contributions...
2025-06-11 15:01:25,305 - reporting.excel_reporter - INFO - Generating comprehensive Excel report...
2025-06-11 15:01:25,395 - reporting.excel_reporter - INFO - Excel report saved: reports\cta_analysis_report_20250611_150125.xlsx
2025-06-11 15:01:26,097 - dashboard.dashboard_generator - INFO - Generating comprehensive dashboard...
2025-06-11 15:01:26,097 - dashboard.dashboard_generator - INFO - Generating static dashboard...
2025-06-11 15:01:28,533 - dashboard.dashboard_generator - INFO - Static dashboard saved: output\cta_analysis\dashboard\cta_dashboard_20250611_150126.png
2025-06-11 15:01:28,534 - dashboard.dashboard_generator - INFO - Generating interactive dashboard...
2025-06-11 15:01:28,806 - dashboard.dashboard_generator - WARNING - Failed to generate interactive dashboard: Trace type 'table' is not compatible with subplot type 'xy'
at grid position (1, 1)

See the docstring for the specs argument to plotly.subplots.make_subplots
for more information on subplot types
2025-06-11 15:01:28,807 - reporting.markdown_reporter - INFO - Generating comprehensive Markdown report...
2025-06-11 15:01:28,809 - reporting.markdown_reporter - INFO - Markdown report saved: reports\cta_analysis_report_20250611_150128.md
2025-06-11 15:01:28,815 - comprehensive_analysis - INFO - ================================================================================
2025-06-11 15:01:28,815 - comprehensive_analysis - INFO - ================================================================================
2025-06-11 15:01:28,816 - comprehensive_analysis - INFO -    CTA Records: 22,905
2025-06-11 15:01:28,816 - comprehensive_analysis - INFO -    Position Records: 103
2025-06-11 15:01:28,816 - comprehensive_analysis - INFO -    Date Range: 2025-01-02 00:00:00 to 2025-06-06 00:00:00
2025-06-11 15:01:28,826 - comprehensive_analysis - INFO -    1. Fixed 3 data quality issues
2025-06-11 15:01:28,827 - comprehensive_analysis - INFO - ================================================================================
2025-06-11 15:01:28,832 - __main__ - INFO -    �����¼��: 22,905
2025-06-11 15:01:28,832 - __main__ - INFO -    ����ʱ�䷶Χ: 2025-01-02 00:00:00 to 2025-06-06 00:00:00
2025-06-11 15:01:28,832 - __main__ - INFO -    ����������: 0
2025-06-11 15:01:28,832 - __main__ - INFO -    ���������: 1
