"""
输出目录管理器
负责管理分析结果的目录结构和文件组织
"""

import os
import shutil
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any
import inspect

from utils.logger import get_module_logger

logger = get_module_logger("OutputManager")


class OutputDirectoryManager:
    """输出目录管理器"""
    
    def __init__(self, base_output_dir: str = "output/cta_analysis"):
        """
        初始化输出目录管理器
        
        Args:
            base_output_dir: 基础输出目录
        """
        self.base_output_dir = Path(base_output_dir)
        self.base_output_dir.mkdir(parents=True, exist_ok=True)
        
    def create_script_output_dir(self, script_name: Optional[str] = None) -> Path:
        """
        为执行脚本创建输出目录
        
        Args:
            script_name: 脚本名称，如果为None则自动检测
            
        Returns:
            Path: 创建的输出目录路径
        """
        if script_name is None:
            # 自动检测调用脚本名称
            script_name = self._get_calling_script_name()
        
        # 清理脚本名称
        script_name = self._clean_script_name(script_name)
        
        # 创建时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建目录结构：base_output_dir/script_name/timestamp/
        script_dir = self.base_output_dir / script_name
        timestamped_dir = script_dir / timestamp
        
        # 创建目录
        timestamped_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        subdirs = ['reports', 'charts', 'data', 'excel']
        for subdir in subdirs:
            (timestamped_dir / subdir).mkdir(exist_ok=True)
        
        logger.info(f"Created output directory: {timestamped_dir}")
        return timestamped_dir
    
    def _get_calling_script_name(self) -> str:
        """获取调用脚本的名称"""
        try:
            # 获取调用栈
            frame = inspect.currentframe()
            while frame:
                filename = frame.f_code.co_filename
                if filename.endswith('.py') and not filename.endswith('output_manager.py'):
                    script_name = Path(filename).stem
                    if script_name not in ['__init__', 'config_manager', 'logger']:
                        return script_name
                frame = frame.f_back
            
            # 如果无法检测到，使用默认名称
            return "analysis"
            
        except Exception as e:
            logger.warning(f"Could not detect calling script: {e}")
            return "analysis"
    
    def _clean_script_name(self, script_name: str) -> str:
        """清理脚本名称"""
        # 移除文件扩展名
        if script_name.endswith('.py'):
            script_name = script_name[:-3]
        
        # 替换特殊字符
        script_name = script_name.replace('_', '-')
        
        # 处理常见的脚本名称映射
        name_mapping = {
            'main': 'run-analysis',
            'backtest': 'backtest-analysis',
            'weight-impact': 'weight-impact-analysis',
            'portfolio-composition': 'portfolio-composition-analysis'
        }
        
        return name_mapping.get(script_name, script_name)
    
    def organize_files(self, output_dir: Path, files: Dict[str, str]) -> Dict[str, str]:
        """
        组织文件到相应的子目录
        
        Args:
            output_dir: 输出目录
            files: 文件字典 {file_type: file_path}
            
        Returns:
            Dict: 组织后的文件路径字典
        """
        organized_files = {}
        
        # 文件类型到子目录的映射
        type_to_subdir = {
            'report': 'reports',
            'markdown': 'reports',
            'chart': 'charts',
            'png': 'charts',
            'jpg': 'charts',
            'jpeg': 'charts',
            'svg': 'charts',
            'excel': 'excel',
            'xlsx': 'excel',
            'xls': 'excel',
            'csv': 'data',
            'json': 'data',
            'pickle': 'data',
            'pkl': 'data'
        }
        
        for file_type, file_path in files.items():
            if not file_path or not os.path.exists(file_path):
                continue
                
            source_path = Path(file_path)
            
            # 确定目标子目录
            subdir = type_to_subdir.get(file_type.lower(), 'data')
            if '.' in file_type:
                # 如果file_type包含扩展名
                ext = file_type.split('.')[-1].lower()
                subdir = type_to_subdir.get(ext, subdir)
            
            # 目标路径
            target_dir = output_dir / subdir
            target_path = target_dir / source_path.name
            
            try:
                # 移动文件
                if source_path != target_path:
                    shutil.move(str(source_path), str(target_path))
                    organized_files[file_type] = str(target_path)
                    logger.debug(f"Moved {file_type} file: {source_path} -> {target_path}")
                else:
                    organized_files[file_type] = str(source_path)
                    
            except Exception as e:
                logger.error(f"Error moving file {source_path}: {e}")
                organized_files[file_type] = str(source_path)  # 保留原路径
        
        return organized_files
    
    def cleanup_old_outputs(self, script_name: str, keep_recent: int = 10):
        """
        清理旧的输出目录
        
        Args:
            script_name: 脚本名称
            keep_recent: 保留最近的目录数量
        """
        script_dir = self.base_output_dir / script_name
        
        if not script_dir.exists():
            return
        
        try:
            # 获取所有时间戳目录
            timestamp_dirs = [d for d in script_dir.iterdir() 
                            if d.is_dir() and self._is_timestamp_dir(d.name)]
            
            # 按时间排序（最新的在前）
            timestamp_dirs.sort(key=lambda x: x.name, reverse=True)
            
            # 删除多余的目录
            for old_dir in timestamp_dirs[keep_recent:]:
                try:
                    shutil.rmtree(old_dir)
                    logger.info(f"Cleaned up old output directory: {old_dir}")
                except Exception as e:
                    logger.error(f"Error cleaning up {old_dir}: {e}")
                    
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def _is_timestamp_dir(self, dirname: str) -> bool:
        """检查目录名是否为时间戳格式"""
        try:
            datetime.strptime(dirname, "%Y%m%d_%H%M%S")
            return True
        except ValueError:
            return False
    
    def get_latest_output_dir(self, script_name: str) -> Optional[Path]:
        """
        获取指定脚本的最新输出目录
        
        Args:
            script_name: 脚本名称
            
        Returns:
            Path: 最新的输出目录，如果不存在则返回None
        """
        script_dir = self.base_output_dir / script_name
        
        if not script_dir.exists():
            return None
        
        try:
            # 获取所有时间戳目录
            timestamp_dirs = [d for d in script_dir.iterdir() 
                            if d.is_dir() and self._is_timestamp_dir(d.name)]
            
            if not timestamp_dirs:
                return None
            
            # 返回最新的目录
            latest_dir = max(timestamp_dirs, key=lambda x: x.name)
            return latest_dir
            
        except Exception as e:
            logger.error(f"Error finding latest output directory: {e}")
            return None
    
    def create_summary_report(self, output_dir: Path, analysis_results: Dict[str, Any]) -> str:
        """
        创建分析摘要报告
        
        Args:
            output_dir: 输出目录
            analysis_results: 分析结果
            
        Returns:
            str: 报告文件路径
        """
        report_path = output_dir / 'reports' / 'analysis_summary.md'
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("# 分析结果摘要\n\n")
                f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write(f"**输出目录**: {output_dir}\n\n")
                
                # 写入分析结果摘要
                if 'success' in analysis_results:
                    status = "✅ 成功" if analysis_results['success'] else "❌ 失败"
                    f.write(f"**分析状态**: {status}\n\n")
                
                if 'files_generated' in analysis_results:
                    f.write("## 生成的文件\n\n")
                    for file_type, file_path in analysis_results['files_generated'].items():
                        f.write(f"- **{file_type}**: `{file_path}`\n")
                    f.write("\n")
                
                # 添加其他关键信息
                for key, value in analysis_results.items():
                    if key not in ['success', 'files_generated', 'error']:
                        f.write(f"**{key}**: {value}\n\n")
            
            logger.info(f"Created summary report: {report_path}")
            return str(report_path)
            
        except Exception as e:
            logger.error(f"Error creating summary report: {e}")
            return ""
