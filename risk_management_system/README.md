# 投资组合风险管理系统

一个全面的Python风险管理系统，用于计算投资组合的**风险价值(VaR)**、**期望损失(ES)**以及**风险贡献度(iVaR和MVaR)**。系统支持三种主要的风险计算方法，所有关键参数都可前置配置，便于用户调整。

## 🎯 核心功能

### 风险度量指标
- **风险价值 (VaR)**: 在特定置信水平和持有期下，投资组合可能遭受的最大百分比损失
- **期望损失 (ES)**: 当损失超过VaR时，预期平均损失的百分比
- **增量VaR (iVaR)**: 移除或增加某资产后，组合VaR的变化
- **边际VaR (MVaR)**: 资产权重微小变化对组合VaR的影响率
- **边际ES (MES)**: 资产权重微小变化对组合ES的影响率

### 权重影响分析 🆕
- **权重敏感性分析**: 分析单资产和多资产权重变动对组合风险的影响
- **边际风险贡献分析**: 计算各资产的边际VaR和边际ES，识别风险集中度
- **风险预算优化建议**: 基于边际风险分析提供具体的权重调整建议

### 多资产组合风险构成分析 🆕
- **自定义权重配置**: 支持多种格式的权重输入和验证
- **风险构成分解**: 详细分析各资产的风险贡献度和边际风险
- **多方案对比分析**: 同时分析多个权重配置方案的风险特征
- **投资组合优化**: 基于风险构成分析提供最优权重配置建议

### 计算方法
1. **参数法 (GARCH模型)**: 基于动态波动率建模，适合实时风险跟踪
2. **历史模拟法**: 使用历史数据的经验分布，无需分布假设，支持Bootstrap方法 🆕
3. **蒙特卡洛模拟法**: 基于随机模拟，处理复杂的非线性情况

### 可视化功能 🆕
- **历史回溯测试图表**: VaR/ES时间序列、违约事件分析、风险分布统计
- **方法对比分析**: 不同计算方法的VaR/ES对比可视化
- **风险管理仪表板**: 综合风险指标展示和统计摘要

## 📁 项目结构

```
risk_management_system/
├── config/                    # 配置管理
│   ├── __init__.py
│   ├── config_manager.py      # 配置管理器
│   └── parameters.yaml        # 系统参数配置
├── core/                      # 核心模块
│   ├── __init__.py
│   ├── data_loader.py         # 数据加载与预处理
│   ├── portfolio_manager.py   # 投资组合管理
│   ├── risk_models.py         # 风险模型实现
│   ├── risk_contribution.py   # 风险贡献度计算
│   ├── weight_sensitivity.py  # 权重敏感性分析 🆕
│   ├── marginal_risk.py       # 边际风险分析 🆕
│   ├── risk_budget_optimizer.py # 风险预算优化 🆕
│   ├── weight_impact_analyzer.py # 权重影响综合分析 🆕
│   ├── weight_config_manager.py # 权重配置管理器 🆕
│   ├── portfolio_composition_analyzer.py # 组合风险构成分析器 🆕
│   ├── multi_portfolio_analyzer.py # 多资产组合分析管理器 🆕
│   └── reporter.py            # 报告生成
├── visualization/             # 可视化模块 🆕
│   ├── __init__.py
│   └── chart_generator.py     # 图表生成器
├── utils/                     # 工具模块
│   ├── __init__.py
│   ├── logger.py              # 日志管理
│   └── output_manager.py      # 输出目录管理器 🆕
├── tests/                     # 测试模块 🆕
│   ├── __init__.py
│   └── test_comprehensive.py  # 综合测试套件
├── data/                      # 数据目录
│   ├── historical_returns.csv # 历史收益率数据
│   └── portfolio_weights.csv  # 投资组合权重
├── results/                   # 结果输出目录
├── logs/                      # 日志目录
├── main.py                    # 主程序
├── requirements.txt           # 依赖包
└── README.md                  # 项目文档
```

## 🚀 快速开始

### 1. 环境配置

```bash
# 克隆或下载项目
cd risk_management_system

# 安装依赖
pip install -r requirements.txt
```

### 2. 创建示例数据

```bash
# 创建示例数据进行测试
python main.py --mode sample
```

### 3. 运行完整分析

```bash
# 运行完整的风险分析
python main.py --mode full

# 使用自定义参数
python main.py --mode full --confidence-level 0.95 --holding-period 1

# 运行历史回溯测试（计算每日VaR/ES时间序列）
python main.py --mode backtest --start-date 2024-01-01 --end-date 2024-12-31

# 运行权重影响分析 🆕
python main.py --mode weight-impact

# 运行多资产组合风险构成分析 🆕
# 单个投资组合分析
python main.py --mode portfolio-composition --composition-type single --weights "股票A:0.3,股票B:0.2,债券A:0.3,债券B:0.2"

# 多投资组合对比分析
python main.py --mode portfolio-composition --composition-type multi --include-predefined

# 投资组合优化分析
python main.py --mode portfolio-composition --composition-type optimization --weights "股票A:0.4,股票B:0.3,债券A:0.2,债券B:0.1"
```

### 4. 快速分析

```bash
# 仅使用参数法进行快速分析
python main.py --mode quick --method parametric

# 使用历史模拟法
python main.py --mode quick --method historical
```

## ⚙️ 参数配置

系统的所有关键参数都在 `config/parameters.yaml` 中前置定义：

### 核心计算参数
```yaml
risk_calculation:
  confidence_level: 0.99          # 置信水平 (99%)
  holding_period: 1               # 持有期 (天)
  risk_free_rate: 0.0            # 无风险利率
```

### 数据处理参数
```yaml
data_processing:
  historical_data_path: "data/historical_returns.csv"
  portfolio_weights_path: "data/portfolio_weights.csv"
  data_window_size: 250           # 历史数据窗口大小
  log_returns: true               # 是否使用对数收益率
```

### 方法特定参数
```yaml
# 参数法 (GARCH)
parametric_method:
  garch_order: [1, 1]            # GARCH 模型阶数
  ewma_lambda: 0.94              # EWMA 衰减因子
  distribution_assumption: "normal"  # 分布假设

# 蒙特卡洛模拟
monte_carlo:
  num_simulations: 10000         # 模拟路径数量
  mc_model_params: "geometric_brownian_motion"

# 权重敏感性分析 🆕
weight_sensitivity:
  single_asset_weight_range: 0.05    # 单资产权重变动范围 (±5%)
  weight_step_size: 0.01             # 权重变动步长 (1%)
  top_assets_count: 3                # 分析的重要资产数量

# 风险预算优化 🆕
risk_budget:
  target_var_reduction: 0.1          # 目标VaR降低比例 (10%)
  max_weight_change: 0.1             # 单个资产最大权重变化

# 多资产组合风险构成分析 🆕
portfolio_composition:
  weight_tolerance: 0.001             # 权重总和容差
  auto_normalize: true                # 自动标准化权重
  max_scenarios: 10                   # 最大对比方案数量

# 自定义权重配置 🆕
custom_weights:
  scenarios:
    conservative:                     # 保守型配置
      股票A: 0.20
      债券A: 0.50
      商品A: 0.30
    balanced:                         # 平衡型配置
      股票A: 0.40
      债券A: 0.40
      商品A: 0.20
```

## 📊 数据格式

### 历史收益率数据 (`data/historical_returns.csv`)
```csv
Date,股票A,股票B,债券A,债券B,商品A
2023-01-01,0.012,-0.008,0.003,0.002,0.015
2023-01-02,-0.005,0.010,0.001,-0.001,-0.008
...
```

### 投资组合权重 (`data/portfolio_weights.csv`)
```csv
Asset,Weight
股票A,0.30
股票B,0.25
债券A,0.20
债券B,0.15
商品A,0.10
```

## 📈 输出结果

### 1. 综合分析报告 (Markdown格式)

#### 表格一：各产品及组合的VaR与ES（百分比损失）

| 产品名称 | 参数法 VaR (%) | 参数法 ES (%) | 历史模拟法 VaR (%) | 历史模拟法 ES (%) | 蒙特卡洛法 VaR (%) | 蒙特卡洛法 ES (%) |
|:---------|:---------------|:--------------|:-------------------|:------------------|:-------------------|:------------------|
| 股票A    | 3.45           | 4.12          | 3.52               | 4.28              | 3.48               | 4.15              |
| 股票B    | 4.23           | 5.01          | 4.18               | 4.95              | 4.25               | 5.08              |
| **当前组合** | **2.15**   | **2.68**      | **2.22**           | **2.75**          | **2.18**           | **2.71**          |

#### 表格二：基于参数法的iVaR与MVaR（百分比贡献）

| 产品名称 | 增量VaR (iVaR) (%) | 边际VaR (MVaR) (%) |
|:---------|:-------------------|:-------------------|
| 股票A    | 0.65               | 2.18               |
| 股票B    | 0.48               | 1.92               |
| **当前组合** | **0.02**       | **2.15**           |

### 2. 历史回溯测试结果 (Excel格式)

系统支持历史回溯测试，使用250天滚动窗口计算每日VaR和ES，保存到 `results/historical_var_es_backtest_*.xlsx`：

| Date       | parametric_var | parametric_es | historical_var | historical_es | monte_carlo_var | monte_carlo_es | portfolio_volatility |
|:-----------|:---------------|:--------------|:---------------|:--------------|:----------------|:---------------|:---------------------|
| 2024-01-01 | 0.0292         | 0.0334        | 0.0293         | 0.0327        | 0.0018          | 0.0021         | 0.0145               |
| 2024-01-02 | 0.0289         | 0.0331        | 0.0298         | 0.0330        | 0.0018          | 0.0021         | 0.0143               |

### 3. 每日结果序列 (Excel格式)

系统自动保存每日计算结果到 `results/daily_risk_metrics.xlsx`，形成时间序列数据：

| Date       | parametric_var | parametric_es | historical_var | historical_es | monte_carlo_var | monte_carlo_es |
|:-----------|:---------------|:--------------|:---------------|:--------------|:----------------|:---------------|
| 2024-01-01 | 0.0215         | 0.0268        | 0.0222         | 0.0275        | 0.0218          | 0.0271         |
| 2024-01-02 | 0.0198         | 0.0245        | 0.0205         | 0.0252        | 0.0201          | 0.0248         |

## 🔧 API使用

### 程序化调用

```python
from main import RiskManagementSystem

# 初始化系统
risk_system = RiskManagementSystem()

# 运行完整分析
results = risk_system.run_full_analysis()

# 快速分析
quick_results = risk_system.run_quick_analysis(method='parametric')

# 历史回溯测试
from datetime import datetime
backtest_results = risk_system.run_historical_backtest(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 12, 31)
)

# 获取系统状态
status = risk_system.get_system_status()

# 运行权重影响分析 🆕
weight_impact_results = risk_system.run_weight_impact_analysis()

# 运行多资产组合风险构成分析 🆕
# 单个投资组合分析
single_results = risk_system.run_portfolio_composition_analysis(
    weights={"股票A": 0.3, "股票B": 0.2, "债券A": 0.3, "债券B": 0.2},
    analysis_type="single"
)

# 多投资组合对比分析
multi_results = risk_system.run_portfolio_composition_analysis(
    analysis_type="multi",
    include_predefined=True
)

# 投资组合优化分析
optimization_results = risk_system.run_portfolio_composition_analysis(
    weights={"股票A": 0.4, "股票B": 0.3, "债券A": 0.2, "债券B": 0.1},
    analysis_type="optimization"
)
```

### 单独使用模块

```python
# 使用风险模型
from core.risk_models import ParametricRiskModel
import pandas as pd

model = ParametricRiskModel()
returns = pd.Series([0.01, -0.02, 0.015, -0.008, 0.012])

var = model.calculate_var(returns, confidence_level=0.95)
es = model.calculate_es(returns, confidence_level=0.95)

# 使用投资组合管理器
from core.portfolio_manager import PortfolioManager

weights = pd.Series([0.4, 0.3, 0.3], index=['A', 'B', 'C'])
returns_data = pd.DataFrame(...)  # 收益率数据

portfolio = PortfolioManager(weights, returns_data)
portfolio_returns = portfolio.calculate_portfolio_returns()
```

## 🎯 权重影响分析详解 🆕

### 分析任务

#### 1. 敏感性分析
- **单资产权重变动**: 选择组合中最有影响力的三个资产，模拟其权重在当前水平上下浮动 ±5%
- **两资产权重协同变动**: 选择相关性高的资产对，设计多个情景模拟协同调整

#### 2. 边际风险贡献分析
- **边际VaR (MVaR)**: 计算每个资产权重增加1%时对组合VaR的影响
- **边际ES (MES)**: 计算每个资产权重增加1%时对组合ES的影响
- **风险贡献度排名**: 识别对组合VaR和ES贡献最大的资产

#### 3. 风险预算与优化建议
- **风险预算分析**: 评估当前投资组合的风险集中度和分散化程度
- **权重调整建议**: 基于边际风险分析提供具体的投资组合调整方案
- **优化效果预测**: 模拟权重调整后的风险改进效果

### 输出结果

#### 1. 可视化图表
- **敏感性分析图**: 权重变化对VaR/ES影响的曲线图
- **边际风险贡献图**: 各资产边际VaR和边际ES的条形图和饼图
- **优化对比图**: 优化前后风险指标和权重配置的对比

#### 2. 详细分析报告
- **敏感性分析报告**: 包含单资产和两资产协同变动的详细分析
- **边际风险分析报告**: 边际VaR/ES的含义解释和风险贡献度排名
- **风险预算优化报告**: 具体的权重调整建议和实施指导

#### 3. 综合Excel数据
- **多工作表结构**: 包含所有分析数据的完整Excel文件
- **权重调整建议表**: 具体的权重调整方案和优先级
- **优化效果预测表**: 预期的风险改进效果

### 专业洞察

#### 边际风险含义
- **正边际VaR**: 增加该资产权重会增加组合风险
- **负边际VaR**: 增加该资产权重会降低组合风险（分散化效应）
- **边际ES**: 衡量极端损失情况下的边际风险贡献

#### 投资组合调整指导
- **风险降低策略**: 减少高边际风险贡献资产的权重
- **分散化改进**: 增加负边际VaR资产的权重
- **风险预算优化**: 在给定VaR限制下最大化预期收益

### 使用示例

```python
# 运行权重影响分析
results = risk_system.run_weight_impact_analysis()

# 查看分析摘要
print(f"分析完成状态: {results['analysis_completed']}")
print(f"最大风险贡献资产: {results['key_findings']['top_risk_contributor']}")
print(f"预期VaR降低: {results['expected_improvements']['var_reduction_pct']:.1f}%")
print(f"权重调整建议数量: {results['recommendations_count']}")
```

### 文件输出结构

```
results/weight_impact_analysis_YYYYMMDD_HHMMSS/
├── charts/                                    # 可视化图表
│   ├── single_asset_sensitivity.png          # 单资产敏感性分析图
│   ├── two_asset_scenarios.png               # 两资产协同分析图
│   ├── marginal_risk_contributions.png       # 边际风险贡献图
│   └── optimization_comparison.png           # 优化对比图
├── reports/                                   # 详细报告
│   ├── sensitivity_analysis_report.md        # 敏感性分析报告
│   ├── marginal_risk_analysis_report.md      # 边际风险分析报告
│   └── risk_budget_optimization_report.md    # 风险预算优化报告
├── comprehensive_weight_impact_report.md     # 综合分析报告
└── weight_impact_analysis_data.xlsx          # 完整分析数据
```

## 🎯 多资产组合风险构成分析详解 🆕

### 功能特点

#### 1. 自定义权重配置功能
- **多种输入格式支持**: JSON、简单格式、文件导入
- **权重验证和标准化**: 自动检查权重总和并标准化
- **预定义方案管理**: 支持保守型、平衡型、激进型等预设方案

#### 2. 风险构成计算与分析
- **组合风险指标**: VaR、ES、有效资产数量、集中度指数
- **各资产风险贡献**: 成分VaR/ES、边际VaR/ES、贡献度百分比
- **分散化效果评估**: 基于有效资产数量和集中度指数

#### 3. 对比分析功能
- **多方案同时分析**: 支持最多10个权重配置方案的对比
- **风险效率排名**: 综合评估各方案的风险控制效果
- **最优方案识别**: 自动识别最低风险、最高分散化等最优方案

#### 4. 投资组合优化
- **当前组合诊断**: 分析现有权重配置的风险特征
- **改进潜力评估**: 计算通过权重调整可实现的风险降低幅度
- **具体调整建议**: 提供优先级明确的权重调整方案

### 权重输入格式

#### 1. 简单格式
```bash
# 命令行格式
--weights "股票A:0.3,股票B:0.2,债券A:0.3,债券B:0.2"
```

#### 2. JSON格式
```bash
# JSON格式
--weights '{"股票A": 0.3, "股票B": 0.2, "债券A": 0.3, "债券B": 0.2}'
```

#### 3. 文件格式
```yaml
# weights_config.yaml
conservative:
  股票A: 0.20
  股票B: 0.15
  债券A: 0.35
  债券B: 0.25
  商品A: 0.05

balanced:
  股票A: 0.25
  股票B: 0.20
  债券A: 0.25
  债券B: 0.20
  商品A: 0.10
```

### 分析类型

#### 1. 单个投资组合分析 (single)
- 分析指定权重配置的风险构成
- 生成详细的风险分解报告
- 提供分散化评估和改进建议

#### 2. 多投资组合对比分析 (multi)
- 同时分析多个权重配置方案
- 生成对比分析图表和排名
- 识别各项指标的最优方案

#### 3. 投资组合优化分析 (optimization)
- 以当前权重为基准进行优化分析
- 生成随机方案进行对比
- 提供具体的权重调整建议

### 使用示例

#### 命令行使用

```bash
# 1. 单个投资组合分析
python main.py --mode portfolio-composition \
    --composition-type single \
    --weights "股票A:0.3,股票B:0.2,债券A:0.3,债券B:0.2"

# 2. 多投资组合对比分析（使用预定义方案）
python main.py --mode portfolio-composition \
    --composition-type multi \
    --include-predefined

# 3. 从文件加载权重配置进行对比
python main.py --mode portfolio-composition \
    --composition-type multi \
    --weights-file weights_config.yaml

# 4. 投资组合优化分析
python main.py --mode portfolio-composition \
    --composition-type optimization \
    --weights "股票A:0.4,股票B:0.3,债券A:0.2,债券B:0.1"
```

#### 程序化调用

```python
from main import RiskManagementSystem

# 初始化系统
risk_system = RiskManagementSystem()

# 1. 单个投资组合分析
single_results = risk_system.run_portfolio_composition_analysis(
    weights={"股票A": 0.3, "股票B": 0.2, "债券A": 0.3, "债券B": 0.2},
    analysis_type="single"
)

# 2. 多投资组合对比分析
multi_results = risk_system.run_portfolio_composition_analysis(
    analysis_type="multi",
    include_predefined=True
)

# 3. 投资组合优化分析
optimization_results = risk_system.run_portfolio_composition_analysis(
    weights={"股票A": 0.4, "股票B": 0.3, "债券A": 0.2, "债券B": 0.1},
    analysis_type="optimization"
)

# 查看分析结果
print(f"分析类型: {single_results['analysis_type']}")
print(f"组合VaR: {single_results['portfolio_var']:.2f}%")
print(f"有效资产数: {single_results['effective_assets']:.1f}")
```

### 输出结果

#### 文件结构

```
results/portfolio_composition_YYYYMMDD_HHMMSS/
├── charts/                                    # 可视化图表
│   ├── portfolio_comparison.png              # 多方案对比图
│   ├── Conservative_composition.png          # 各方案风险构成图
│   ├── Balanced_composition.png
│   └── Aggressive_composition.png
├── reports/                                   # 详细报告
│   ├── portfolio_comparison_report.md        # 对比分析报告
│   ├── Conservative_composition_report.md    # 各方案详细报告
│   ├── Balanced_composition_report.md
│   └── Aggressive_composition_report.md
├── analysis_summary.md                       # 综合摘要报告
└── multi_portfolio_analysis_data.xlsx        # 完整分析数据
```

#### 可视化图表

1. **风险构成分解图**: 权重分布、VaR贡献度、边际风险
2. **多方案对比图**: 风险指标对比、分散化指标、风险-分散化散点图
3. **权重分布热力图**: 直观显示各方案的权重配置差异

#### 分析报告

1. **单方案报告**: 详细的风险构成分析和投资建议
2. **对比分析报告**: 多方案排名和选择指导
3. **优化分析报告**: 改进潜力和具体调整建议

### 专业洞察

#### 风险构成指标解释

- **成分VaR/ES**: 各资产对组合总风险的绝对贡献
- **边际VaR/ES**: 增加该资产1%权重对组合风险的影响
- **风险贡献度**: 各资产风险贡献占总风险的百分比
- **有效资产数量**: 1/HHI，衡量投资组合的有效分散程度

#### 投资决策指导

1. **风险厌恶型**: 选择最低VaR/ES方案
2. **分散化偏好型**: 选择最高有效资产数量方案
3. **平衡型**: 选择综合评分最优方案
4. **优化导向型**: 基于当前组合进行渐进式调整

### 集成特性

- **与权重影响分析协同**: 可结合权重敏感性分析结果
- **配置文件驱动**: 支持灵活的参数配置
- **多格式输出**: Markdown报告、Excel数据、PNG图表
- **命令行和API接口**: 支持批处理和程序化调用
```

## 🧪 测试

运行测试套件：

```bash
# 运行所有测试
pytest tests/

# 运行特定测试
pytest tests/test_risk_models.py -v

# 生成覆盖率报告
pytest --cov=core tests/
```

## 📝 系统特点

### 1. 模块化设计
- 每个模块职责单一，便于维护和扩展
- 清晰的接口定义，支持独立测试
- 松耦合架构，易于替换和升级

### 2. 参数前置
- 所有关键参数集中在配置文件中
- 支持运行时参数调整
- 便于不同场景的快速切换

### 3. 多方法支持
- 三种主流VaR计算方法
- 自动方法比较和验证
- 灵活的方法选择机制

### 4. 完整的风险分析
- VaR和ES的同时计算
- 详细的风险贡献度分析
- 自动化的报告生成

### 5. 数据管理
- 自动数据对齐和验证
- 缺失值处理
- 时间序列结果存储

## ⚠️ 注意事项

1. **数据质量**: 确保历史收益率数据的质量和完整性
2. **模型假设**: 理解各种方法的假设和适用条件
3. **参数设置**: 根据实际情况调整置信水平和持有期
4. **结果解释**: VaR和ES是基于历史数据的统计估计，实际损失可能超出预测
5. **定期更新**: 建议定期更新模型参数和重新校准
6. **方法一致性**: 三种方法的VaR结果应在合理范围内保持一致（通常差异<30%）

## 🔮 扩展功能

系统设计支持以下扩展：

- 添加新的风险模型（如Copula模型）
- 支持更多资产类别（期权、期货等）
- 集成实时数据源
- 添加压力测试功能
- 支持多币种组合
- 集成优化算法

## 📞 技术支持

如有问题或建议，请：

1. 查看日志文件 (`logs/` 目录)
2. 运行系统状态检查 (`python main.py --mode status`)
3. 查看测试结果确认系统完整性

---

**免责声明**: 本系统仅供学术研究和教育目的使用，不构成投资建议。使用者应当根据自己的判断进行投资决策，并承担相应风险。
