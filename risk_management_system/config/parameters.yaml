# 风险管理系统核心参数配置

# 核心计算参数
risk_calculation:
  confidence_level: 0.95          # 置信水平 (99%)
  holding_period: 1               # 持有期 (天)
  risk_free_rate: 0.0            # 无风险利率

# 风险指标配置
risk_metrics:
  # VaR和ES配置
  var_confidence_levels: [0.95, 0.99]  # 置信水平
  var_time_horizons: [1, 5, 10]        # 时间周期（天）
  rolling_window: 250                   # 滚动窗口（交易日）

  # Bootstrap方法配置
  use_bootstrap_method: False            # 是否使用Bootstrap方法计算VaR/ES

  # 夏普比率配置
  risk_free_rate: 0.02                 # 无风险利率

  # 最大回撤配置
  drawdown_threshold: 0.05             # 回撤阈值

# 数据处理参数
data_processing:
  historical_data_path: "data/historical_returns.csv"
  portfolio_weights_path: "data/portfolio_weights.csv"
  data_window_size: 250           # 历史数据窗口大小 (天)
  log_returns: true               # 是否使用对数收益率
  min_data_points: 100            # 最小数据点数量

# 参数法 (GARCH) 特定参数
parametric_method:
  garch_order: [1, 1]            # GARCH 模型阶数 (p, q)
  ewma_lambda: 0.94              # EWMA 衰减因子
  distribution_assumption: "normal"  # 分布假设: 'normal' 或 'student_t'
  max_iter: 1000                 # GARCH 拟合最大迭代次数
  use_simplified_var: true       # 使用简化VaR计算确保与边际VaR一致性 🆕

# 历史模拟法参数
historical_simulation:
  bootstrap_samples: 1000        # 自助法样本数量
  use_weighted_hs: false         # 是否使用加权历史模拟

# 蒙特卡洛模拟法参数
monte_carlo:
  num_simulations: 10000         # 模拟路径数量
  num_steps_mc: 1                # 蒙特卡洛模拟时间步长
  mc_model_params: "geometric_brownian_motion"  # 风险因子模型
  random_seed: 42                # 随机种子

# 风险贡献度计算参数
risk_contribution:
  delta_weight: 0.01             # 权重变化量 (用于数值计算MVaR)
  use_analytical_mvar: true      # 是否使用解析解计算MVaR

# 权重敏感性分析参数
weight_sensitivity:
  single_asset_weight_range: 0.05    # 单资产权重变动范围 (±5%)
  weight_step_size: 0.01             # 权重变动步长 (1%)
  top_assets_count: 3                # 分析的重要资产数量
  scenario_count: 3                  # 两资产协同变动情景数量

# 边际风险分析参数
marginal_risk:
  calculate_marginal_es: true        # 是否计算边际ES
  risk_attribution_threshold: 0.01   # 风险贡献度阈值

# 风险计算验证参数 🆕
risk_validation:
  var_consistency_tolerance: 0.05      # VaR一致性验证容差 (5%)
  es_consistency_tolerance: 0.05       # ES一致性验证容差 (5%)
  marginal_var_tolerance: 0.1          # 边际VaR验证容差 (10%)
  enable_strict_validation: false      # 是否启用严格验证

# 风险预算优化参数
risk_budget:
  target_var_reduction: 0.1          # 目标VaR降低比例 (10%)
  max_weight_change: 0.1             # 单个资产最大权重变化
  min_asset_weight: 0.01             # 资产最小权重
  max_asset_weight: 0.5              # 资产最大权重

# 多资产组合风险构成分析参数 🆕
portfolio_composition:
  weight_tolerance: 0.001             # 权重总和容差
  auto_normalize: true                # 自动标准化权重
  min_weight_threshold: 0.001         # 最小权重阈值
  max_scenarios: 10                   # 最大对比方案数量

# 自定义权重配置 🆕
custom_weights:
  # 示例权重配置方案
  scenarios:
    conservative:                     # 保守型配置
      股票A: 0.20
      股票B: 0.15
      债券A: 0.35
      债券B: 0.25
      商品A: 0.05

    balanced:                         # 平衡型配置
      股票A: 0.25
      股票B: 0.20
      债券A: 0.25
      债券B: 0.20
      商品A: 0.10

    aggressive:                       # 激进型配置
      股票A: 0.35
      股票B: 0.30
      债券A: 0.15
      债券B: 0.10
      商品A: 0.10

# 输出设置
output:
  results_path: "results/"
  daily_results_file: "daily_risk_metrics.xlsx"
  report_format: "markdown"      # 输出格式: 'markdown' 或 'excel'
  save_intermediate_results: true

# 日志设置
logging:
  level: "INFO"                  # 日志级别
  log_file: "logs/risk_system.log"
  max_file_size: "10MB"
  backup_count: 5
