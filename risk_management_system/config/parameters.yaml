custom_weights:
  scenarios:
    aggressive:
      债券A: 0.15
      债券B: 0.1
      商品A: 0.1
      股票A: 0.35
      股票B: 0.3
    balanced:
      债券A: 0.25
      债券B: 0.2
      商品A: 0.1
      股票A: 0.25
      股票B: 0.2
    conservative:
      债券A: 0.35
      债券B: 0.25
      商品A: 0.05
      股票A: 0.2
      股票B: 0.15
data_processing:
  data_window_size: 250
  historical_data_path: data/historical_returns.csv
  log_returns: true
  min_data_points: 100
  portfolio_weights_path: data/portfolio_weights.csv
historical_simulation:
  bootstrap_samples: 1000
  use_weighted_hs: false
logging:
  backup_count: 5
  level: INFO
  log_file: logs/risk_system.log
  max_file_size: 10MB
marginal_risk:
  calculate_marginal_es: true
  risk_attribution_threshold: 0.01
monte_carlo:
  mc_model_params: geometric_brownian_motion
  num_simulations: 10000
  num_steps_mc: 1
  random_seed: 42
output:
  daily_results_file: daily_risk_metrics.xlsx
  report_format: markdown
  results_path: results/
  save_intermediate_results: true
parametric_method:
  distribution_assumption: normal
  ewma_lambda: 0.94
  garch_order:
  - 1
  - 1
  max_iter: 1000
  use_simplified_var: true
  volatility_window: 250
portfolio_composition:
  auto_normalize: true
  max_scenarios: 10
  min_weight_threshold: 0.001
  weight_tolerance: 0.001
risk_budget:
  max_asset_weight: 0.5
  max_weight_change: 0.1
  min_asset_weight: 0.01
  target_var_reduction: 0.1
risk_calculation:
  confidence_level: 0.95
  holding_period: 1
  risk_free_rate: 0.0
risk_contribution:
  delta_weight: 0.01
  use_analytical_mvar: true
risk_metrics:
  drawdown_threshold: 0.05
  risk_free_rate: 0.02
  rolling_window: 250
  use_bootstrap_method: false
  var_confidence_levels:
  - 0.95
  - 0.99
  var_time_horizons:
  - 1
  - 5
  - 10
risk_validation:
  enable_strict_validation: false
  es_consistency_tolerance: 0.05
  marginal_var_tolerance: 0.1
  var_consistency_tolerance: 0.05
weight_sensitivity:
  scenario_count: 3
  single_asset_weight_range: 0.05
  top_assets_count: 3
  weight_step_size: 0.01
