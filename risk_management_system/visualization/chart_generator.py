"""
风险管理系统可视化模块
提供历史回溯测试和风险指标的可视化功能
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import seaborn as sns

# 设置跨平台中文字体
import platform
import matplotlib.font_manager as fm

def setup_chinese_fonts():
    """设置跨平台中文字体支持"""
    system = platform.system()

    if system == "Windows":
        # Windows字体优先级
        font_candidates = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        # macOS字体优先级
        font_candidates = ['PingFang SC', 'Arial Unicode MS', 'Heiti SC', 'STHeiti']
    else:  # Linux和其他系统
        # Linux字体优先级
        font_candidates = ['Noto Sans CJK SC', 'WenQuanYi Micro Hei', 'DejaVu Sans']

    # 通用回退字体
    font_candidates.extend(['DejaVu Sans', 'Liberation Sans', 'Arial', 'sans-serif'])

    # 查找可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    selected_font = None

    for font in font_candidates:
        if font in available_fonts:
            selected_font = font
            break

    if selected_font:
        plt.rcParams['font.sans-serif'] = [selected_font] + font_candidates
        print(f"Selected font: {selected_font}")
    else:
        # 如果没有找到合适的字体，使用默认设置
        plt.rcParams['font.sans-serif'] = font_candidates
        print("Using default font fallback")

    plt.rcParams['axes.unicode_minus'] = False
    return selected_font

# 初始化字体设置
setup_chinese_fonts()

class RiskVisualizationEngine:
    """风险管理可视化引擎"""
    
    def __init__(self, output_dir: str = "results/charts"):
        """
        初始化可视化引擎

        Args:
            output_dir: 图表输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 确保字体设置正确
        self.selected_font = setup_chinese_fonts()

        # 设置图表样式
        try:
            plt.style.use('seaborn-v0_8')
        except:
            # 如果seaborn-v0_8不可用，使用默认样式
            plt.style.use('default')

        # 颜色配置
        self.colors = {
            'var_95': '#FF6B6B',      # 红色 - 95% VaR
            'var_99': '#FF0000',      # 深红色 - 99% VaR
            'es_95': '#4ECDC4',       # 青色 - 95% ES
            'es_99': '#45B7D1',       # 蓝色 - 99% ES
            'breach': '#FFD93D',      # 黄色 - 违约事件
            'portfolio': '#6C5CE7',   # 紫色 - 组合
            'background': '#F8F9FA'   # 背景色
        }

    def _ensure_font_settings(self):
        """确保字体设置正确应用"""
        setup_chinese_fonts()

    def create_historical_backtest_chart(self, historical_data: pd.DataFrame,
                                       confidence_levels: List[float] = [0.95, 0.99],
                                       save_path: Optional[str] = None) -> str:
        """
        创建历史回溯测试图表

        Args:
            historical_data: 历史VaR/ES数据
            confidence_levels: 置信水平列表
            save_path: 保存路径

        Returns:
            str: 保存的图表文件路径
        """
        # 确保字体设置正确
        self._ensure_font_settings()

        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('历史VaR/ES回溯测试分析', fontsize=16, fontweight='bold')
        
        # 确保数据索引是日期类型
        if not isinstance(historical_data.index, pd.DatetimeIndex):
            historical_data.index = pd.to_datetime(historical_data.index)
        
        # 1. VaR时间序列图
        ax1 = axes[0, 0]
        self._plot_var_timeseries(ax1, historical_data, confidence_levels)
        
        # 2. ES时间序列图
        ax2 = axes[0, 1]
        self._plot_es_timeseries(ax2, historical_data, confidence_levels)
        
        # 3. VaR违约事件分析
        ax3 = axes[1, 0]
        self._plot_var_breach_events(ax3, historical_data, confidence_levels)
        
        # 4. 风险指标分布图
        ax4 = axes[1, 1]
        self._plot_risk_distribution(ax4, historical_data, confidence_levels)
        
        plt.tight_layout()
        
        # 保存图表
        if save_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            save_path = self.output_dir / f"historical_backtest_chart_{timestamp}.png"
        else:
            save_path = Path(save_path)
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        return str(save_path)
    
    def _plot_var_timeseries(self, ax, data: pd.DataFrame, confidence_levels: List[float]):
        """绘制VaR时间序列"""
        ax.set_title('VaR时间序列', fontweight='bold', fontsize=12)
        
        # 绘制不同置信水平的VaR
        for conf_level in confidence_levels:
            conf_pct = int(conf_level * 100)
            
            # 查找对应的VaR列
            var_columns = [col for col in data.columns if f'var' in col.lower()]
            
            for col in var_columns:
                if f'{conf_pct}' in col or (conf_level == 0.95 and '95' not in col and '99' not in col):
                    method_name = col.replace('_var', '').replace('_', ' ').title()
                    color = self.colors[f'var_{conf_pct}'] if f'var_{conf_pct}' in self.colors else None
                    
                    ax.plot(data.index, data[col] * 100, 
                           label=f'{method_name} VaR ({conf_pct}%)',
                           linewidth=1.5, color=color, alpha=0.8)
        
        ax.set_ylabel('VaR (%)')
        ax.legend(loc='upper right', fontsize=9)
        ax.grid(True, alpha=0.3)
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    def _plot_es_timeseries(self, ax, data: pd.DataFrame, confidence_levels: List[float]):
        """绘制ES时间序列"""
        ax.set_title('ES时间序列', fontweight='bold', fontsize=12)
        
        # 绘制不同置信水平的ES
        for conf_level in confidence_levels:
            conf_pct = int(conf_level * 100)
            
            # 查找对应的ES列
            es_columns = [col for col in data.columns if f'es' in col.lower()]
            
            for col in es_columns:
                if f'{conf_pct}' in col or (conf_level == 0.95 and '95' not in col and '99' not in col):
                    method_name = col.replace('_es', '').replace('_', ' ').title()
                    color = self.colors[f'es_{conf_pct}'] if f'es_{conf_pct}' in self.colors else None
                    
                    ax.plot(data.index, data[col] * 100, 
                           label=f'{method_name} ES ({conf_pct}%)',
                           linewidth=1.5, color=color, alpha=0.8)
        
        ax.set_ylabel('ES (%)')
        ax.legend(loc='upper right', fontsize=9)
        ax.grid(True, alpha=0.3)
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    def _plot_var_breach_events(self, ax, data: pd.DataFrame, confidence_levels: List[float]):
        """绘制VaR违约事件"""
        ax.set_title('VaR违约事件分析', fontweight='bold', fontsize=12)
        
        # 计算实际收益率（如果有的话）
        if 'portfolio_returns' in data.columns:
            actual_returns = data['portfolio_returns'] * 100
            ax.plot(data.index, actual_returns, label='实际收益率', 
                   color='gray', alpha=0.6, linewidth=1)
        
        # 绘制VaR线并标记违约事件
        for conf_level in confidence_levels:
            conf_pct = int(conf_level * 100)
            var_columns = [col for col in data.columns if f'var' in col.lower()]
            
            for col in var_columns:
                if f'{conf_pct}' in col or (conf_level == 0.95 and '95' not in col and '99' not in col):
                    var_values = data[col] * 100
                    color = self.colors[f'var_{conf_pct}'] if f'var_{conf_pct}' in self.colors else None
                    
                    # 绘制VaR线
                    ax.plot(data.index, -var_values, 
                           label=f'VaR ({conf_pct}%)', 
                           color=color, linewidth=2, linestyle='--')
                    
                    # 标记违约事件
                    if 'portfolio_returns' in data.columns:
                        breaches = actual_returns < -var_values
                        if breaches.any():
                            breach_dates = data.index[breaches]
                            breach_returns = actual_returns[breaches]
                            ax.scatter(breach_dates, breach_returns, 
                                     color=self.colors['breach'], s=30, 
                                     label=f'VaR违约 ({conf_pct}%)', alpha=0.8)
        
        ax.set_ylabel('收益率 (%)')
        ax.legend(loc='lower right', fontsize=9)
        ax.grid(True, alpha=0.3)
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    def _plot_risk_distribution(self, ax, data: pd.DataFrame, confidence_levels: List[float]):
        """绘制风险指标分布"""
        ax.set_title('风险指标分布', fontweight='bold', fontsize=12)
        
        # 收集所有VaR和ES数据
        var_data = []
        es_data = []
        labels = []
        
        for conf_level in confidence_levels:
            conf_pct = int(conf_level * 100)
            
            # VaR数据
            var_columns = [col for col in data.columns if f'var' in col.lower()]
            for col in var_columns:
                if f'{conf_pct}' in col or (conf_level == 0.95 and '95' not in col and '99' not in col):
                    var_data.append(data[col].dropna() * 100)
                    labels.append(f'VaR {conf_pct}%')
            
            # ES数据
            es_columns = [col for col in data.columns if f'es' in col.lower()]
            for col in es_columns:
                if f'{conf_pct}' in col or (conf_level == 0.95 and '95' not in col and '99' not in col):
                    es_data.append(data[col].dropna() * 100)
                    labels.append(f'ES {conf_pct}%')
        
        # 绘制箱线图
        all_data = var_data + es_data
        if all_data:
            bp = ax.boxplot(all_data, labels=labels[:len(all_data)], patch_artist=True)
            
            # 设置颜色
            colors = []
            for i, label in enumerate(labels[:len(all_data)]):
                if 'VaR 95' in label:
                    colors.append(self.colors['var_95'])
                elif 'VaR 99' in label:
                    colors.append(self.colors['var_99'])
                elif 'ES 95' in label:
                    colors.append(self.colors['es_95'])
                elif 'ES 99' in label:
                    colors.append(self.colors['es_99'])
                else:
                    colors.append('#95A5A6')
            
            for patch, color in zip(bp['boxes'], colors):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
        
        ax.set_ylabel('风险值 (%)')
        ax.grid(True, alpha=0.3)
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    def create_var_es_comparison_chart(self, historical_data: pd.DataFrame,
                                     methods: List[str] = ['parametric', 'historical', 'monte_carlo'],
                                     save_path: Optional[str] = None) -> str:
        """
        创建不同方法的VaR/ES对比图表

        Args:
            historical_data: 历史数据
            methods: 计算方法列表
            save_path: 保存路径

        Returns:
            str: 保存的图表文件路径
        """
        # 确保字体设置正确
        self._ensure_font_settings()

        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle('不同方法VaR/ES对比分析', fontsize=16, fontweight='bold')

        # VaR对比
        ax1 = axes[0]
        ax1.set_title('VaR方法对比', fontweight='bold')

        for method in methods:
            var_col = f'{method}_var'
            if var_col in historical_data.columns:
                ax1.plot(historical_data.index, historical_data[var_col] * 100,
                        label=f'{method.title()} VaR', linewidth=1.5, alpha=0.8)

        ax1.set_ylabel('VaR (%)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)

        # ES对比
        ax2 = axes[1]
        ax2.set_title('ES方法对比', fontweight='bold')

        for method in methods:
            es_col = f'{method}_es'
            if es_col in historical_data.columns:
                ax2.plot(historical_data.index, historical_data[es_col] * 100,
                        label=f'{method.title()} ES', linewidth=1.5, alpha=0.8)

        ax2.set_ylabel('ES (%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        # 保存图表
        if save_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            save_path = self.output_dir / f"var_es_comparison_{timestamp}.png"
        else:
            save_path = Path(save_path)

        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        return str(save_path)

    def create_risk_metrics_dashboard(self, historical_data: pd.DataFrame,
                                    save_path: Optional[str] = None) -> str:
        """
        创建风险指标仪表板

        Args:
            historical_data: 历史数据
            save_path: 保存路径

        Returns:
            str: 保存的图表文件路径
        """
        # 确保字体设置正确
        self._ensure_font_settings()

        fig = plt.figure(figsize=(20, 12))
        gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)

        fig.suptitle('风险管理仪表板', fontsize=20, fontweight='bold')

        # 1. VaR时间序列 (占2列)
        ax1 = fig.add_subplot(gs[0, :2])
        self._plot_var_timeseries(ax1, historical_data, [0.95, 0.99])

        # 2. ES时间序列 (占2列)
        ax2 = fig.add_subplot(gs[0, 2:])
        self._plot_es_timeseries(ax2, historical_data, [0.95, 0.99])

        # 3. 违约事件分析 (占2列)
        ax3 = fig.add_subplot(gs[1, :2])
        self._plot_var_breach_events(ax3, historical_data, [0.95, 0.99])

        # 4. 风险分布 (占2列)
        ax4 = fig.add_subplot(gs[1, 2:])
        self._plot_risk_distribution(ax4, historical_data, [0.95, 0.99])

        # 5. 统计摘要表格 (占全行)
        ax5 = fig.add_subplot(gs[2, :])
        self._plot_statistics_table(ax5, historical_data)

        # 保存图表
        if save_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            save_path = self.output_dir / f"risk_dashboard_{timestamp}.png"
        else:
            save_path = Path(save_path)

        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        return str(save_path)

    def _plot_statistics_table(self, ax, data: pd.DataFrame):
        """绘制统计摘要表格"""
        ax.set_title('风险指标统计摘要', fontweight='bold', fontsize=12)
        ax.axis('off')

        # 收集统计数据
        stats_data = []
        columns = ['指标', '均值(%)', '标准差(%)', '最小值(%)', '最大值(%)', '中位数(%)']

        # VaR和ES列
        risk_columns = [col for col in data.columns if 'var' in col.lower() or 'es' in col.lower()]

        for col in risk_columns:
            if col in data.columns:
                stats = data[col].describe()
                method_name = col.replace('_var', '').replace('_es', '').replace('_', ' ').title()
                risk_type = 'VaR' if 'var' in col.lower() else 'ES'

                stats_data.append([
                    f'{method_name} {risk_type}',
                    f'{stats["mean"]*100:.2f}',
                    f'{stats["std"]*100:.2f}',
                    f'{stats["min"]*100:.2f}',
                    f'{stats["max"]*100:.2f}',
                    f'{stats["50%"]*100:.2f}'
                ])

        if stats_data:
            # 创建表格
            table = ax.table(cellText=stats_data, colLabels=columns,
                           cellLoc='center', loc='center',
                           bbox=[0, 0, 1, 1])

            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1, 2)

            # 设置表头样式
            for i in range(len(columns)):
                table[(0, i)].set_facecolor('#4472C4')
                table[(0, i)].set_text_props(weight='bold', color='white')

            # 设置行颜色
            for i in range(1, len(stats_data) + 1):
                for j in range(len(columns)):
                    if i % 2 == 0:
                        table[(i, j)].set_facecolor('#F2F2F2')
                    else:
                        table[(i, j)].set_facecolor('white')
