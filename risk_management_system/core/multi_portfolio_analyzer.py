"""
多资产组合分析管理器
整合权重配置管理和组合风险构成分析功能
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Union, Optional, Any, Tuple
import warnings
warnings.filterwarnings('ignore')

from config.config_manager import config_manager
from core.weight_config_manager import WeightConfigManager
from core.portfolio_composition_analyzer import PortfolioCompositionAnalyzer
from core.data_loader import DataLoader
from utils.logger import get_module_logger

logger = get_module_logger("MultiPortfolioAnalyzer")


class MultiPortfolioAnalyzer:
    """多资产组合分析管理器"""
    
    def __init__(self):
        """初始化多资产组合分析管理器"""
        self.config = config_manager.get_config()
        self.composition_config = self.config.get('portfolio_composition', {})
        
        # 初始化组件
        self.weight_manager = WeightConfigManager()
        self.composition_analyzer = PortfolioCompositionAnalyzer()
        self.data_loader = DataLoader()
        self.output_directory = None
        self.generated_files = {}

        logger.info("Multi-portfolio analyzer initialized")

    def set_output_directory(self, output_dir: Path):
        """设置输出目录"""
        self.output_directory = Path(output_dir)
        logger.info(f"Output directory set to: {self.output_directory}")

    def get_generated_files(self) -> Dict[str, str]:
        """获取生成的文件列表"""
        return self.generated_files.copy()
    
    def run_single_portfolio_analysis(self, weights: Union[Dict, pd.Series, str], 
                                    returns_data: Optional[pd.DataFrame] = None,
                                    scenario_name: str = "Custom Portfolio",
                                    save_results: bool = True) -> Dict[str, Any]:
        """
        运行单个投资组合的完整分析
        
        Args:
            weights: 权重配置
            returns_data: 收益率数据，如果为None则自动加载
            scenario_name: 方案名称
            save_results: 是否保存结果
            
        Returns:
            Dict: 分析结果
        """
        logger.info(f"开始单个投资组合分析: {scenario_name}")
        
        try:
            # 1. 加载数据
            if returns_data is None:
                returns_data = self.data_loader.load_historical_returns()
            
            # 2. 验证和标准化权重
            if isinstance(weights, str):
                weights = self.weight_manager.parse_weight_input(weights)
            
            weights_series, is_normalized = self.weight_manager.validate_weights(weights)
            
            if is_normalized:
                logger.info(f"权重已自动标准化")
            
            # 3. 对齐数据和权重
            aligned_returns, aligned_weights = self.data_loader.align_data_and_weights(
                returns_data, weights_series
            )
            
            if aligned_returns.empty:
                raise ValueError("没有找到共同的资产数据")
            
            # 4. 运行风险构成分析
            analysis_result = self.composition_analyzer.analyze_single_portfolio(
                aligned_weights, aligned_returns, scenario_name
            )
            
            # 5. 保存结果
            if save_results:
                self._save_single_analysis_results(analysis_result, aligned_returns)
            
            logger.info(f"完成单个投资组合分析: {scenario_name}")
            return {
                'success': True,
                'scenario_name': scenario_name,
                'analysis_result': analysis_result,
                'aligned_data_shape': aligned_returns.shape,
                'normalized_weights': is_normalized
            }
            
        except Exception as e:
            logger.error(f"单个投资组合分析失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'scenario_name': scenario_name
            }
    
    def run_multi_portfolio_analysis(self, scenarios: Optional[Dict[str, Union[Dict, pd.Series]]] = None,
                                   returns_data: Optional[pd.DataFrame] = None,
                                   include_predefined: bool = True,
                                   save_results: bool = True) -> Dict[str, Any]:
        """
        运行多个投资组合的对比分析
        
        Args:
            scenarios: 自定义权重配置方案
            returns_data: 收益率数据，如果为None则自动加载
            include_predefined: 是否包含预定义方案
            save_results: 是否保存结果
            
        Returns:
            Dict: 多组合分析结果
        """
        logger.info("开始多投资组合对比分析")
        
        try:
            # 1. 加载数据
            if returns_data is None:
                returns_data = self.data_loader.load_historical_returns()
            
            # 2. 收集所有权重配置方案
            all_scenarios = {}
            
            # 加载预定义方案
            if include_predefined:
                predefined_scenarios = self.weight_manager.load_predefined_scenarios()
                all_scenarios.update(predefined_scenarios)
                logger.info(f"加载 {len(predefined_scenarios)} 个预定义方案")
            
            # 添加自定义方案
            if scenarios:
                for scenario_name, weights in scenarios.items():
                    try:
                        weights_series = self.weight_manager.create_weight_scenario(
                            scenario_name, weights
                        )
                        all_scenarios[scenario_name] = weights_series
                    except Exception as e:
                        logger.error(f"处理自定义方案 {scenario_name} 失败: {e}")
                        continue
                
                logger.info(f"添加 {len(scenarios)} 个自定义方案")
            
            if not all_scenarios:
                raise ValueError("没有有效的权重配置方案")
            
            # 3. 对齐所有方案的数据
            aligned_scenarios = {}
            for scenario_name, weights in all_scenarios.items():
                try:
                    aligned_returns, aligned_weights = self.data_loader.align_data_and_weights(
                        returns_data, weights
                    )
                    if not aligned_returns.empty:
                        aligned_scenarios[scenario_name] = aligned_weights
                    else:
                        logger.warning(f"方案 {scenario_name} 没有匹配的数据，跳过")
                except Exception as e:
                    logger.error(f"对齐方案 {scenario_name} 数据失败: {e}")
                    continue
            
            if not aligned_scenarios:
                raise ValueError("没有方案能够与数据对齐")
            
            # 使用第一个成功对齐的方案的数据作为基准
            first_scenario = list(aligned_scenarios.keys())[0]
            base_returns, _ = self.data_loader.align_data_and_weights(
                returns_data, aligned_scenarios[first_scenario]
            )
            
            # 4. 运行多组合分析
            multi_analysis_results = self.composition_analyzer.analyze_multiple_portfolios(
                aligned_scenarios, base_returns
            )
            
            # 5. 保存结果
            if save_results:
                self._save_multi_analysis_results(multi_analysis_results, base_returns)
            
            logger.info(f"完成 {len(aligned_scenarios)} 个投资组合的对比分析")
            return {
                'success': True,
                'num_scenarios': len(aligned_scenarios),
                'scenario_names': list(aligned_scenarios.keys()),
                'multi_analysis_results': multi_analysis_results,
                'aligned_data_shape': base_returns.shape
            }
            
        except Exception as e:
            logger.error(f"多投资组合分析失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def run_optimization_analysis(self, current_weights: Union[Dict, pd.Series],
                                returns_data: Optional[pd.DataFrame] = None,
                                num_random_scenarios: int = 5,
                                save_results: bool = True) -> Dict[str, Any]:
        """
        运行投资组合优化分析
        
        Args:
            current_weights: 当前权重配置
            returns_data: 收益率数据
            num_random_scenarios: 随机方案数量
            save_results: 是否保存结果
            
        Returns:
            Dict: 优化分析结果
        """
        logger.info("开始投资组合优化分析")
        
        try:
            # 1. 加载数据
            if returns_data is None:
                returns_data = self.data_loader.load_historical_returns()
            
            # 2. 解析和验证当前权重
            if isinstance(current_weights, str):
                current_weights = self.weight_manager.parse_weight_input(current_weights)
            current_weights_series, _ = self.weight_manager.validate_weights(current_weights)
            
            # 3. 对齐数据
            aligned_returns, aligned_current_weights = self.data_loader.align_data_and_weights(
                returns_data, current_weights_series
            )
            
            # 4. 分析当前组合
            current_analysis = self.composition_analyzer.analyze_single_portfolio(
                aligned_current_weights, aligned_returns, "Current Portfolio"
            )
            
            # 5. 生成权重调整建议
            recommendations = self.composition_analyzer.generate_weight_recommendations(
                aligned_current_weights, aligned_returns
            )
            
            # 6. 生成随机对比方案
            assets = aligned_current_weights.index.tolist()
            random_scenarios = self.weight_manager.generate_random_scenarios(
                assets, num_random_scenarios
            )
            
            # 7. 加载预定义方案进行对比
            predefined_scenarios = self.weight_manager.load_predefined_scenarios()
            
            # 8. 整合所有方案
            all_scenarios = {'Current': aligned_current_weights}
            all_scenarios.update(random_scenarios)
            
            # 只包含能对齐的预定义方案
            for name, weights in predefined_scenarios.items():
                try:
                    _, aligned_weights = self.data_loader.align_data_and_weights(
                        aligned_returns, weights
                    )
                    all_scenarios[name] = aligned_weights
                except:
                    continue
            
            # 9. 运行对比分析
            multi_analysis = self.composition_analyzer.analyze_multiple_portfolios(
                all_scenarios, aligned_returns
            )
            
            # 10. 寻找最优方案
            optimal_scenario, optimal_analysis = self.composition_analyzer.find_optimal_portfolio(
                all_scenarios, aligned_returns, 'min_var'
            )
            
            # 11. 整合结果
            optimization_results = {
                'current_analysis': current_analysis,
                'recommendations': recommendations,
                'multi_analysis': multi_analysis,
                'optimal_scenario': optimal_scenario,
                'optimal_analysis': optimal_analysis,
                'improvement_potential': self._calculate_improvement_potential(
                    current_analysis, optimal_analysis
                )
            }
            
            # 12. 保存结果
            if save_results:
                self._save_optimization_results(optimization_results, aligned_returns)
            
            logger.info("完成投资组合优化分析")
            return {
                'success': True,
                'optimization_results': optimization_results,
                'num_scenarios_compared': len(all_scenarios),
                'optimal_scenario': optimal_scenario
            }
            
        except Exception as e:
            logger.error(f"投资组合优化分析失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _calculate_improvement_potential(self, current_analysis: Dict[str, Any], 
                                       optimal_analysis: Dict[str, Any]) -> Dict[str, float]:
        """计算改进潜力"""
        current_var = current_analysis['portfolio_metrics']['portfolio_var']
        current_es = current_analysis['portfolio_metrics']['portfolio_es']
        optimal_var = optimal_analysis['portfolio_metrics']['portfolio_var']
        optimal_es = optimal_analysis['portfolio_metrics']['portfolio_es']
        
        return {
            'var_improvement_pct': (current_var - optimal_var) / current_var * 100,
            'es_improvement_pct': (current_es - optimal_es) / current_es * 100,
            'var_improvement_abs': (current_var - optimal_var) * 100,
            'es_improvement_abs': (current_es - optimal_es) * 100
        }
    
    def load_weights_from_command_line(self, weight_string: str) -> pd.Series:
        """
        从命令行参数加载权重
        
        Args:
            weight_string: 权重字符串
            
        Returns:
            pd.Series: 权重配置
        """
        weights_dict = self.weight_manager.parse_weight_input(weight_string)
        weights_series, _ = self.weight_manager.validate_weights(weights_dict)
        return weights_series
    
    def load_weights_from_file(self, file_path: str) -> Dict[str, pd.Series]:
        """
        从文件加载权重配置
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 权重配置方案
        """
        return self.weight_manager.load_weights_from_file(file_path)

    def _save_single_analysis_results(self, analysis_result: Dict[str, Any],
                                    returns_data: pd.DataFrame) -> Dict[str, str]:
        """
        保存单个投资组合分析结果

        Args:
            analysis_result: 分析结果
            returns_data: 收益率数据

        Returns:
            Dict: 生成的文件路径
        """
        scenario_name = analysis_result['scenario_name'].replace(' ', '_')

        # 使用新的输出目录结构
        if self.output_directory:
            analysis_dir = self.output_directory
        else:
            # 回退到旧的方式
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            results_dir = Path(self.config.get('output', {}).get('results_path', 'results'))
            analysis_dir = results_dir / f"portfolio_composition_{scenario_name}_{timestamp}"
            analysis_dir.mkdir(parents=True, exist_ok=True)

        generated_files = {}

        try:
            # 1. 生成可视化图表
            charts_dir = analysis_dir / "charts"
            charts_dir.mkdir(exist_ok=True)

            composition_chart_path = charts_dir / f"{scenario_name}_composition.png"
            self.composition_analyzer.visualize_portfolio_composition(
                analysis_result, str(composition_chart_path)
            )
            generated_files['composition_chart'] = str(composition_chart_path)

            # 2. 生成分析报告
            reports_dir = analysis_dir / "reports"
            reports_dir.mkdir(exist_ok=True)

            composition_report = self.composition_analyzer.generate_composition_report(analysis_result)
            report_path = reports_dir / f"{scenario_name}_composition_report.md"
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(composition_report)
            generated_files['composition_report'] = str(report_path)

            # 3. 保存数据到Excel
            excel_dir = analysis_dir / "excel"
            excel_dir.mkdir(exist_ok=True)
            excel_path = excel_dir / f"{scenario_name}_analysis_data.xlsx"
            self._save_single_analysis_to_excel(analysis_result, excel_path)
            generated_files['excel_data'] = str(excel_path)

            # 4. 保存原始数据
            data_dir = analysis_dir / "data"
            data_dir.mkdir(exist_ok=True)
            data_path = data_dir / f"{scenario_name}_portfolio_data.json"
            import json
            with open(data_path, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, indent=2, default=str)
            generated_files['data_json'] = str(data_path)

            # 更新生成的文件列表
            self.generated_files.update(generated_files)

            logger.info(f"单个投资组合分析结果已保存到: {analysis_dir}")
            return generated_files

        except Exception as e:
            logger.error(f"保存单个分析结果失败: {e}")
            return generated_files

    def _save_multi_analysis_results(self, multi_analysis_results: Dict[str, Any],
                                   returns_data: pd.DataFrame) -> Dict[str, str]:
        """
        保存多投资组合对比分析结果

        Args:
            multi_analysis_results: 多组合分析结果
            returns_data: 收益率数据

        Returns:
            Dict: 生成的文件路径
        """
        # 使用新的输出目录结构
        if self.output_directory:
            analysis_dir = self.output_directory
        else:
            # 回退到旧的方式
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            results_dir = Path(self.config.get('output', {}).get('results_path', 'results'))
            analysis_dir = results_dir / f"multi_portfolio_comparison_{timestamp}"
            analysis_dir.mkdir(parents=True, exist_ok=True)

        generated_files = {}

        try:
            # 1. 生成对比可视化图表
            charts_dir = analysis_dir / "charts"
            charts_dir.mkdir(exist_ok=True)

            comparison_chart_path = charts_dir / "portfolio_comparison.png"
            self.composition_analyzer.visualize_portfolio_comparison(
                multi_analysis_results, str(comparison_chart_path)
            )
            generated_files['comparison_chart'] = str(comparison_chart_path)

            # 2. 为每个投资组合生成单独的图表
            individual_analyses = multi_analysis_results['individual_analyses']
            for scenario_name, analysis in individual_analyses.items():
                individual_chart_path = charts_dir / f"{scenario_name}_composition.png"
                self.composition_analyzer.visualize_portfolio_composition(
                    analysis, str(individual_chart_path)
                )
                generated_files[f'{scenario_name}_chart'] = str(individual_chart_path)

            # 3. 生成对比分析报告
            reports_dir = analysis_dir / "reports"
            reports_dir.mkdir(exist_ok=True)

            comparison_report = self.composition_analyzer.generate_comparison_report(multi_analysis_results)
            comparison_report_path = reports_dir / "portfolio_comparison_report.md"
            with open(comparison_report_path, 'w', encoding='utf-8') as f:
                f.write(comparison_report)
            generated_files['comparison_report'] = str(comparison_report_path)

            # 4. 为每个投资组合生成单独的报告
            for scenario_name, analysis in individual_analyses.items():
                individual_report = self.composition_analyzer.generate_composition_report(analysis)
                individual_report_path = reports_dir / f"{scenario_name}_composition_report.md"
                with open(individual_report_path, 'w', encoding='utf-8') as f:
                    f.write(individual_report)
                generated_files[f'{scenario_name}_report'] = str(individual_report_path)

            # 5. 保存完整数据到Excel
            excel_dir = analysis_dir / "excel"
            excel_dir.mkdir(exist_ok=True)
            excel_path = excel_dir / "multi_portfolio_analysis_data.xlsx"
            self._save_multi_analysis_to_excel(multi_analysis_results, excel_path)
            generated_files['excel_data'] = str(excel_path)

            # 6. 保存原始数据
            data_dir = analysis_dir / "data"
            data_dir.mkdir(exist_ok=True)
            data_path = data_dir / "multi_portfolio_data.json"
            import json
            with open(data_path, 'w', encoding='utf-8') as f:
                json.dump(multi_analysis_results, f, indent=2, default=str)
            generated_files['data_json'] = str(data_path)

            # 7. 生成综合摘要报告
            summary_report = self._generate_multi_analysis_summary(multi_analysis_results)
            summary_path = analysis_dir / "reports" / "analysis_summary.md"
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(summary_report)
            generated_files['summary_report'] = str(summary_path)

            # 更新生成的文件列表
            self.generated_files.update(generated_files)

            logger.info(f"多投资组合对比分析结果已保存到: {analysis_dir}")
            return generated_files

        except Exception as e:
            logger.error(f"保存多组合分析结果失败: {e}")
            return generated_files

    def _save_optimization_results(self, optimization_results: Dict[str, Any],
                                 returns_data: pd.DataFrame) -> Dict[str, str]:
        """
        保存投资组合优化分析结果

        Args:
            optimization_results: 优化分析结果
            returns_data: 收益率数据

        Returns:
            Dict: 生成的文件路径
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_dir = Path(self.config.get('output', {}).get('results_path', 'results'))

        # 创建结果目录
        analysis_dir = results_dir / f"portfolio_optimization_{timestamp}"
        analysis_dir.mkdir(parents=True, exist_ok=True)

        generated_files = {}

        try:
            # 1. 保存多组合对比分析结果
            multi_files = self._save_multi_analysis_results(
                optimization_results['multi_analysis'], returns_data
            )
            generated_files.update(multi_files)

            # 2. 生成优化专用报告
            reports_dir = analysis_dir / "reports"
            reports_dir.mkdir(exist_ok=True)

            optimization_report = self._generate_optimization_report(optimization_results)
            optimization_report_path = reports_dir / "portfolio_optimization_report.md"
            with open(optimization_report_path, 'w', encoding='utf-8') as f:
                f.write(optimization_report)
            generated_files['optimization_report'] = str(optimization_report_path)

            # 3. 保存权重调整建议
            recommendations = optimization_results['recommendations']
            recommendations_path = analysis_dir / "weight_adjustment_recommendations.xlsx"
            self._save_recommendations_to_excel(recommendations, recommendations_path)
            generated_files['recommendations_excel'] = str(recommendations_path)

            logger.info(f"投资组合优化分析结果已保存到: {analysis_dir}")
            return generated_files

        except Exception as e:
            logger.error(f"保存优化分析结果失败: {e}")
            return generated_files

    def _save_single_analysis_to_excel(self, analysis_result: Dict[str, Any],
                                     excel_path: Path) -> None:
        """保存单个分析结果到Excel"""
        try:
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:

                # 1. 投资组合概览
                portfolio_metrics = analysis_result['portfolio_metrics']
                overview_data = {
                    '指标': ['组合VaR(%)', '组合ES(%)', '资产数量', '有效资产数量', '集中度指数'],
                    '数值': [
                        portfolio_metrics['portfolio_var'] * 100,
                        portfolio_metrics['portfolio_es'] * 100,
                        portfolio_metrics['num_assets'],
                        portfolio_metrics['effective_assets'],
                        portfolio_metrics['concentration_hhi']
                    ]
                }
                overview_df = pd.DataFrame(overview_data)
                overview_df.to_excel(writer, sheet_name='投资组合概览', index=False)

                # 2. 权重配置
                weights_data = []
                for asset, weight in analysis_result['weights'].items():
                    weights_data.append({
                        '资产': asset,
                        '权重': weight,
                        '权重(%)': weight * 100
                    })
                weights_df = pd.DataFrame(weights_data)
                weights_df.to_excel(writer, sheet_name='权重配置', index=False)

                # 3. 风险构成分析
                asset_analysis = analysis_result['asset_analysis']
                risk_data = []
                for asset, analysis in asset_analysis.items():
                    risk_data.append({
                        '资产': asset,
                        '权重(%)': analysis['weight_pct'],
                        '边际VaR(%)': analysis['marginal_var'] * 100,
                        '边际ES(%)': analysis['marginal_es'] * 100,
                        '成分VaR(%)': analysis['component_var'] * 100,
                        '成分ES(%)': analysis['component_es'] * 100,
                        'VaR贡献度(%)': analysis['var_contribution_pct'],
                        'ES贡献度(%)': analysis['es_contribution_pct']
                    })
                risk_df = pd.DataFrame(risk_data)
                risk_df.to_excel(writer, sheet_name='风险构成分析', index=False)

                # 4. 权重统计
                weight_stats = analysis_result['weight_statistics']
                stats_data = {
                    '统计指标': ['总权重', '资产数量', '正权重资产数', '最大权重', '最小权重',
                               '平均权重', '权重标准差', '集中度指数', '有效资产数'],
                    '数值': [
                        weight_stats['total_weight'],
                        weight_stats['num_assets'],
                        weight_stats['num_positive_weights'],
                        weight_stats['max_weight'],
                        weight_stats['min_weight'],
                        weight_stats['mean_weight'],
                        weight_stats['std_weight'],
                        weight_stats['concentration_hhi'],
                        weight_stats['effective_assets']
                    ]
                }
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='权重统计', index=False)

            logger.info(f"单个分析数据已保存到Excel: {excel_path}")

        except Exception as e:
            logger.error(f"保存单个分析数据到Excel失败: {e}")

    def _save_multi_analysis_to_excel(self, multi_analysis_results: Dict[str, Any],
                                    excel_path: Path) -> None:
        """保存多组合分析结果到Excel"""
        try:
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:

                # 1. 对比概览
                comparison_table = multi_analysis_results['comparison_analysis']['comparison_table']
                comparison_table.to_excel(writer, sheet_name='投资组合对比')

                # 2. 排名分析
                rankings = multi_analysis_results['comparison_analysis']['rankings']
                rankings_data = {
                    '指标': ['最低VaR方案', '最低ES方案', '最高分散化方案', '最低集中度方案', '综合最优方案'],
                    '最优方案': [
                        rankings['lowest_var'],
                        rankings['lowest_es'],
                        rankings['highest_diversification'],
                        rankings['lowest_concentration'],
                        multi_analysis_results['comparison_analysis']['best_overall_scenario']
                    ]
                }
                rankings_df = pd.DataFrame(rankings_data)
                rankings_df.to_excel(writer, sheet_name='排名分析', index=False)

                # 3. 汇总统计
                summary_stats = multi_analysis_results['summary_statistics']

                # VaR统计
                var_stats_data = {
                    '统计量': ['平均值', '标准差', '最小值', '最大值', '变化范围'],
                    'VaR(%)': [
                        summary_stats['var_statistics']['mean'],
                        summary_stats['var_statistics']['std'],
                        summary_stats['var_statistics']['min'],
                        summary_stats['var_statistics']['max'],
                        summary_stats['var_statistics']['range']
                    ],
                    'ES(%)': [
                        summary_stats['es_statistics']['mean'],
                        summary_stats['es_statistics']['std'],
                        summary_stats['es_statistics']['min'],
                        summary_stats['es_statistics']['max'],
                        summary_stats['es_statistics']['range']
                    ]
                }
                stats_df = pd.DataFrame(var_stats_data)
                stats_df.to_excel(writer, sheet_name='风险统计', index=False)

                # 4. 各方案详细权重
                individual_analyses = multi_analysis_results['individual_analyses']

                # 获取所有资产
                all_assets = set()
                for analysis in individual_analyses.values():
                    all_assets.update(analysis['weights'].keys())
                all_assets = sorted(list(all_assets))

                # 创建权重对比表
                weights_comparison = pd.DataFrame(index=all_assets)
                for scenario_name, analysis in individual_analyses.items():
                    weights = analysis['weights']
                    weights_comparison[scenario_name] = [weights.get(asset, 0) * 100 for asset in all_assets]

                weights_comparison.to_excel(writer, sheet_name='权重对比(%)')

            logger.info(f"多组合分析数据已保存到Excel: {excel_path}")

        except Exception as e:
            logger.error(f"保存多组合分析数据到Excel失败: {e}")

    def _save_recommendations_to_excel(self, recommendations: Dict[str, Any],
                                     excel_path: Path) -> None:
        """保存权重调整建议到Excel"""
        try:
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:

                # 权重调整建议
                if recommendations.get('weight_adjustments'):
                    adj_data = []
                    for asset, adj in recommendations['weight_adjustments'].items():
                        adj_data.append({
                            '资产': asset,
                            '当前权重(%)': adj['current_weight'] * 100,
                            '建议权重(%)': adj['suggested_weight'] * 100,
                            '权重变化(%)': adj['suggested_change'] * 100,
                            '调整原因': adj['reason'],
                            '优先级': adj['priority']
                        })

                    adj_df = pd.DataFrame(adj_data)
                    adj_df.to_excel(writer, sheet_name='权重调整建议', index=False)

                # 风险改进预期
                improvement_data = {
                    '指标': ['当前VaR(%)', '目标VaR(%)', '目标降低比例(%)'],
                    '数值': [
                        recommendations.get('current_var', 0),
                        recommendations.get('target_var', 0),
                        recommendations.get('target_reduction_pct', 0)
                    ]
                }
                improvement_df = pd.DataFrame(improvement_data)
                improvement_df.to_excel(writer, sheet_name='改进目标', index=False)

            logger.info(f"权重调整建议已保存到Excel: {excel_path}")

        except Exception as e:
            logger.error(f"保存权重调整建议到Excel失败: {e}")

    def _generate_multi_analysis_summary(self, multi_analysis_results: Dict[str, Any]) -> str:
        """生成多组合分析摘要报告"""
        comparison_analysis = multi_analysis_results['comparison_analysis']
        summary_stats = multi_analysis_results['summary_statistics']

        report_lines = []

        report_lines.append("# 多投资组合分析摘要")
        report_lines.append("")
        report_lines.append(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"**对比方案数量**: {summary_stats['num_scenarios']}")
        report_lines.append("")

        # 关键发现
        report_lines.append("## 🔍 关键发现")
        report_lines.append("")

        rankings = comparison_analysis['rankings']
        report_lines.append(f"- **综合最优方案**: {comparison_analysis['best_overall_scenario']}")
        report_lines.append(f"- **最低风险方案**: {rankings['lowest_var']} (VaR)")
        report_lines.append(f"- **最高分散化方案**: {rankings['highest_diversification']}")

        # 风险范围
        risk_range = comparison_analysis['risk_range']
        report_lines.append(f"- **VaR变化范围**: {risk_range['var_range'][0]:.2f}% - {risk_range['var_range'][1]:.2f}%")
        report_lines.append(f"- **分散化范围**: {risk_range['diversification_range'][0]:.1f} - {risk_range['diversification_range'][1]:.1f} 有效资产")
        report_lines.append("")

        # 投资建议
        report_lines.append("## 💡 投资建议")
        report_lines.append("")
        report_lines.append(f"推荐选择 **{comparison_analysis['best_overall_scenario']}** 方案，该方案在风险控制和分散化方面表现均衡。")
        report_lines.append("")
        report_lines.append("详细分析请参考完整的对比分析报告。")

        return "\n".join(report_lines)

    def _generate_optimization_report(self, optimization_results: Dict[str, Any]) -> str:
        """生成投资组合优化报告"""
        current_analysis = optimization_results['current_analysis']
        recommendations = optimization_results['recommendations']
        optimal_analysis = optimization_results['optimal_analysis']
        improvement_potential = optimization_results['improvement_potential']

        report_lines = []

        report_lines.append("# 投资组合优化分析报告")
        report_lines.append("")
        report_lines.append(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"**最优方案**: {optimization_results['optimal_scenario']}")
        report_lines.append("")

        # 当前组合分析
        current_metrics = current_analysis['portfolio_metrics']
        report_lines.append("## 1. 当前投资组合分析")
        report_lines.append("")
        report_lines.append(f"- **当前VaR**: {current_metrics['portfolio_var'] * 100:.2f}%")
        report_lines.append(f"- **当前ES**: {current_metrics['portfolio_es'] * 100:.2f}%")
        report_lines.append(f"- **有效资产数**: {current_metrics['effective_assets']:.1f}")
        report_lines.append(f"- **集中度指数**: {current_metrics['concentration_hhi']:.3f}")
        report_lines.append("")

        # 优化潜力
        report_lines.append("## 2. 优化潜力分析")
        report_lines.append("")
        optimal_metrics = optimal_analysis['portfolio_metrics']
        report_lines.append(f"- **最优VaR**: {optimal_metrics['portfolio_var'] * 100:.2f}%")
        report_lines.append(f"- **最优ES**: {optimal_metrics['portfolio_es'] * 100:.2f}%")
        report_lines.append(f"- **VaR改进潜力**: {improvement_potential['var_improvement_pct']:.1f}%")
        report_lines.append(f"- **ES改进潜力**: {improvement_potential['es_improvement_pct']:.1f}%")
        report_lines.append("")

        # 权重调整建议
        if recommendations.get('weight_adjustments'):
            report_lines.append("## 3. 权重调整建议")
            report_lines.append("")

            high_priority = {k: v for k, v in recommendations['weight_adjustments'].items()
                           if v['priority'] == 'high'}

            if high_priority:
                report_lines.append("### 高优先级调整")
                for asset, adj in high_priority.items():
                    change_direction = "减少" if adj['suggested_change'] < 0 else "增加"
                    report_lines.append(f"- **{asset}**: {change_direction}权重至 {adj['suggested_weight']*100:.1f}% "
                                       f"(当前: {adj['current_weight']*100:.1f}%)")
                report_lines.append("")

        # 实施建议
        report_lines.append("## 4. 实施建议")
        report_lines.append("")
        report_lines.append("1. **立即执行高优先级调整**")
        report_lines.append("2. **逐步实施中优先级调整**")
        report_lines.append("3. **定期监控风险指标变化**")
        report_lines.append("4. **根据市场环境调整策略**")
        report_lines.append("")

        report_lines.append("详细的对比分析和可视化图表请参考相关文件。")

        return "\n".join(report_lines)

    def get_analysis_summary(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取分析结果摘要

        Args:
            analysis_results: 分析结果

        Returns:
            Dict: 分析摘要
        """
        if analysis_results.get('success'):
            if 'multi_analysis_results' in analysis_results:
                # 多组合分析摘要
                multi_results = analysis_results['multi_analysis_results']
                comparison = multi_results['comparison_analysis']

                return {
                    'analysis_type': 'multi_portfolio',
                    'num_scenarios': analysis_results['num_scenarios'],
                    'best_scenario': comparison['best_overall_scenario'],
                    'risk_range': comparison['risk_range'],
                    'rankings': comparison['rankings']
                }

            elif 'optimization_results' in analysis_results:
                # 优化分析摘要
                opt_results = analysis_results['optimization_results']

                return {
                    'analysis_type': 'optimization',
                    'optimal_scenario': analysis_results['optimal_scenario'],
                    'num_scenarios_compared': analysis_results['num_scenarios_compared'],
                    'improvement_potential': opt_results['improvement_potential']
                }

            elif 'analysis_result' in analysis_results:
                # 单组合分析摘要
                analysis = analysis_results['analysis_result']
                metrics = analysis['portfolio_metrics']

                return {
                    'analysis_type': 'single_portfolio',
                    'scenario_name': analysis_results['scenario_name'],
                    'portfolio_var': metrics['portfolio_var'] * 100,
                    'portfolio_es': metrics['portfolio_es'] * 100,
                    'effective_assets': metrics['effective_assets'],
                    'concentration_hhi': metrics['concentration_hhi']
                }

        return {
            'analysis_type': 'failed',
            'error': analysis_results.get('error', 'Unknown error')
        }
