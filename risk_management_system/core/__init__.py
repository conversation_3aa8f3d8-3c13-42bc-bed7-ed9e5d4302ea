"""
Core modules for risk management system

This package contains the core functionality for portfolio risk management,
including data loading, portfolio management, risk models, and analysis tools.
"""

__version__ = "2.1.0"

# Core modules
from .data_loader import DataLoader
from .portfolio_manager import PortfolioManager
from .risk_models import RiskModelFactory
from .risk_contribution import RiskContributionCalculator
from .reporter import RiskReporter

# Analysis modules
from .weight_impact_analyzer import WeightImpactAnalyzer
from .multi_portfolio_analyzer import MultiPortfolioAnalyzer
from .weight_config_manager import WeightConfigManager

__all__ = [
    'DataLoader',
    'PortfolioManager',
    'RiskModelFactory',
    'RiskContributionCalculator',
    'RiskReporter',
    'WeightImpactAnalyzer',
    'MultiPortfolioAnalyzer',
    'WeightConfigManager'
]
