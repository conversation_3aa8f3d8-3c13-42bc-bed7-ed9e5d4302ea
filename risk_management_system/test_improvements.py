#!/usr/bin/env python3
"""
风险管理系统改进验证脚本
验证四项技术检查和优化的实施效果
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from main import RiskManagementSystem
from config.config_manager import config_manager
from core.risk_models import RiskModelFactory
from utils.output_manager import OutputDirectoryManager
from visualization.chart_generator import RiskVisualizationEngine


class ImprovementValidator:
    """改进验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.test_results = {}
        self.risk_system = None
    
    def setup_test_environment(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        try:
            self.risk_system = RiskManagementSystem()
            print("   ✅ 风险管理系统初始化成功")
            return True
        except Exception as e:
            print(f"   ❌ 测试环境设置失败: {e}")
            return False
    
    def test_historical_simulation_data_window(self):
        """测试历史模拟法数据窗口验证"""
        print("🧪 测试1: 历史模拟法数据窗口验证")
        print("-" * 40)
        
        try:
            # 创建测试数据
            np.random.seed(42)
            test_returns = pd.Series(np.random.normal(0, 0.02, 300))  # 300个数据点
            
            # 获取历史模拟模型
            hs_model = RiskModelFactory.create_model('historical')
            
            # 测试数据窗口验证
            print(f"   原始数据长度: {len(test_returns)}")
            
            # 计算VaR和ES
            var, es = hs_model.calculate_var_es(test_returns)
            
            print(f"   VaR: {var:.4f}, ES: {es:.4f}")
            print("   ✅ 历史模拟法数据窗口验证正常")
            
            # 测试数据不足的情况
            short_returns = test_returns.head(50)  # 只有50个数据点
            var_short, es_short = hs_model.calculate_var_es(short_returns)
            print(f"   短数据VaR: {var_short:.4f}, ES: {es_short:.4f}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 历史模拟法数据窗口验证失败: {e}")
            return False
    
    def test_parametric_volatility_window(self):
        """测试参数法波动率计算周期"""
        print("🧪 测试2: 参数法波动率计算周期检查")
        print("-" * 40)
        
        try:
            # 检查配置文件中的volatility_window设置
            param_config = config_manager.get_parametric_method_config()
            volatility_window = getattr(param_config, 'volatility_window', None)
            
            if volatility_window is None:
                print("   ❌ 配置文件中缺少volatility_window设置")
                return False
            
            print(f"   配置的波动率窗口: {volatility_window}")
            
            # 创建测试数据
            np.random.seed(42)
            test_returns = pd.Series(np.random.normal(0, 0.02, 400))  # 400个数据点
            
            # 获取参数法模型
            param_model = RiskModelFactory.create_model('parametric')
            
            # 测试波动率计算
            var, es = param_model.calculate_var_es(test_returns)
            
            print(f"   参数法VaR: {var:.4f}, ES: {es:.4f}")
            print("   ✅ 参数法波动率计算周期检查正常")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 参数法波动率计算周期检查失败: {e}")
            return False
    
    def test_bootstrap_configuration(self):
        """测试Bootstrap方法配置控制"""
        print("🧪 测试3: Bootstrap方法配置控制")
        print("-" * 40)
        
        try:
            # 检查配置文件中的use_bootstrap_method设置
            config = config_manager.get_config()
            risk_metrics = config.get('risk_metrics', {})
            use_bootstrap = risk_metrics.get('use_bootstrap_method', None)
            
            if use_bootstrap is None:
                print("   ❌ 配置文件中缺少use_bootstrap_method设置")
                return False
            
            print(f"   当前Bootstrap配置: {use_bootstrap}")
            
            # 创建测试数据
            np.random.seed(42)
            test_returns = pd.Series(np.random.normal(0, 0.02, 250))
            
            # 测试启用Bootstrap方法
            config_manager.update_parameter('risk_metrics', 'use_bootstrap_method', True)
            hs_model = RiskModelFactory.create_model('historical')
            var_bootstrap, es_bootstrap = hs_model.calculate_var_es(test_returns)
            print(f"   Bootstrap启用 - VaR: {var_bootstrap:.4f}, ES: {es_bootstrap:.4f}")
            
            # 测试禁用Bootstrap方法
            config_manager.update_parameter('risk_metrics', 'use_bootstrap_method', False)
            hs_model = RiskModelFactory.create_model('historical')
            var_standard, es_standard = hs_model.calculate_var_es(test_returns)
            print(f"   Bootstrap禁用 - VaR: {var_standard:.4f}, ES: {es_standard:.4f}")
            
            # 恢复原始配置
            config_manager.update_parameter('risk_metrics', 'use_bootstrap_method', use_bootstrap)
            
            print("   ✅ Bootstrap方法配置控制正常")
            return True
            
        except Exception as e:
            print(f"   ❌ Bootstrap方法配置控制失败: {e}")
            return False
    
    def test_output_directory_structure(self):
        """测试输出目录结构"""
        print("🧪 测试4: 输出目录结构")
        print("-" * 40)
        
        try:
            # 测试输出管理器
            output_manager = OutputDirectoryManager()
            
            # 创建测试输出目录
            test_output_dir = output_manager.create_script_output_dir("test-validation")
            print(f"   创建的输出目录: {test_output_dir}")
            
            # 验证子目录是否创建
            expected_subdirs = ['reports', 'charts', 'data', 'excel']
            for subdir in expected_subdirs:
                subdir_path = test_output_dir / subdir
                if not subdir_path.exists():
                    print(f"   ❌ 子目录 {subdir} 不存在")
                    return False
                print(f"   ✅ 子目录 {subdir} 存在")
            
            # 测试可视化引擎
            viz_engine = RiskVisualizationEngine(str(test_output_dir / 'charts'))
            
            # 创建测试数据
            np.random.seed(42)
            dates = pd.date_range('2023-01-01', periods=100, freq='D')
            test_data = pd.DataFrame({
                'parametric_var': np.random.uniform(0.01, 0.03, 100),
                'historical_var': np.random.uniform(0.01, 0.03, 100),
                'parametric_es': np.random.uniform(0.015, 0.035, 100),
                'historical_es': np.random.uniform(0.015, 0.035, 100),
            }, index=dates)
            
            # 生成测试图表
            chart_path = viz_engine.create_historical_backtest_chart(test_data)
            print(f"   生成的图表: {chart_path}")
            
            if Path(chart_path).exists():
                print("   ✅ 可视化图表生成成功")
            else:
                print("   ❌ 可视化图表生成失败")
                return False
            
            print("   ✅ 输出目录结构测试正常")
            return True
            
        except Exception as e:
            print(f"   ❌ 输出目录结构测试失败: {e}")
            return False
    
    def test_full_analysis_integration(self):
        """测试完整分析集成"""
        print("🧪 测试5: 完整分析集成")
        print("-" * 40)
        
        try:
            # 运行完整分析
            results = self.risk_system.run_full_analysis()
            
            if not results.get('success', False):
                print(f"   ❌ 完整分析失败: {results.get('error', 'Unknown error')}")
                return False
            
            # 检查输出目录
            output_dir = results.get('output_directory')
            if output_dir and Path(output_dir).exists():
                print(f"   ✅ 输出目录创建成功: {output_dir}")
            else:
                print("   ❌ 输出目录创建失败")
                return False
            
            # 检查生成的文件
            files_generated = results.get('files_generated', {})
            for file_type, file_path in files_generated.items():
                if Path(file_path).exists():
                    print(f"   ✅ {file_type} 文件生成成功")
                else:
                    print(f"   ❌ {file_type} 文件生成失败")
            
            print("   ✅ 完整分析集成测试正常")
            return True
            
        except Exception as e:
            print(f"   ❌ 完整分析集成测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有验证测试"""
        print("🚀 风险管理系统改进验证")
        print("=" * 60)
        
        # 设置测试环境
        if not self.setup_test_environment():
            return False
        
        # 定义测试列表
        tests = [
            ("历史模拟法数据窗口验证", self.test_historical_simulation_data_window),
            ("参数法波动率计算周期检查", self.test_parametric_volatility_window),
            ("Bootstrap方法配置控制", self.test_bootstrap_configuration),
            ("输出目录结构", self.test_output_directory_structure),
            ("完整分析集成", self.test_full_analysis_integration)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 测试: {test_name}")
            
            try:
                if test_func():
                    passed += 1
                    self.test_results[test_name] = "PASSED"
                else:
                    self.test_results[test_name] = "FAILED"
            except Exception as e:
                self.test_results[test_name] = f"ERROR: {e}"
                print(f"❌ {test_name} 测试出错: {e}")
        
        # 测试结果摘要
        print("\n" + "=" * 60)
        print(f"📊 验证结果摘要: {passed}/{total} 通过")
        
        # 详细结果
        print("\n📋 详细验证结果:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result == "PASSED" else "❌"
            print(f"   {status_icon} {test_name}: {result}")
        
        if passed == total:
            print("\n🎉 所有改进验证通过! 系统优化成功")
            return True
        else:
            print(f"\n⚠️  {total - passed} 个验证失败，请检查相关改进")
            return False


def main():
    """主验证函数"""
    validator = ImprovementValidator()
    success = validator.run_all_tests()
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
