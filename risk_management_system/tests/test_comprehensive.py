#!/usr/bin/env python3
"""
风险管理系统综合测试套件
整合所有功能模块的测试，包括权重影响分析和投资组合构成分析
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import json

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from main import RiskManagementSystem
from core.multi_portfolio_analyzer import MultiPortfolioAnalyzer
from core.weight_config_manager import WeightConfigManager
from core.weight_impact_analyzer import WeightImpactAnalyzer
from core.data_loader import DataLoader
from core.portfolio_manager import PortfolioManager


class ComprehensiveTestSuite:
    """综合测试套件"""
    
    def __init__(self):
        """初始化测试套件"""
        self.risk_system = None
        self.test_results = {}
    
    def setup_test_environment(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        try:
            self.risk_system = RiskManagementSystem()
            print("   ✅ 风险管理系统初始化成功")
            return True
        except Exception as e:
            print(f"   ❌ 测试环境设置失败: {e}")
            return False
    
    def test_weight_config_manager(self):
        """测试权重配置管理器"""
        print("🧪 测试权重配置管理器...")
        
        try:
            weight_manager = WeightConfigManager()
            
            # 1. 测试权重验证
            test_weights = {"股票A": 0.3, "股票B": 0.2, "债券A": 0.3, "债券B": 0.2}
            validated_weights, is_normalized = weight_manager.validate_weights(test_weights)
            assert abs(validated_weights.sum() - 1.0) < 1e-6, "权重总和应为1"
            
            # 2. 测试权重解析
            weight_string = "股票A:0.3,股票B:0.2,债券A:0.3,债券B:0.2"
            parsed_weights = weight_manager.parse_weight_input(weight_string)
            assert len(parsed_weights) == 4, "应解析出4个资产"
            
            # 3. 测试预定义方案加载
            predefined_scenarios = weight_manager.load_predefined_scenarios()
            assert len(predefined_scenarios) > 0, "应有预定义方案"
            
            print("   ✅ 权重配置管理器测试通过")
            return True
            
        except Exception as e:
            print(f"   ❌ 权重配置管理器测试失败: {e}")
            return False
    
    def test_weight_impact_analysis(self):
        """测试权重影响分析"""
        print("🧪 测试权重影响分析...")
        
        try:
            # 运行权重影响分析
            results = self.risk_system.run_weight_impact_analysis()
            
            # 验证结果
            assert results.get('success', False), "权重影响分析应成功"
            assert 'analysis_summary' in results, "应包含分析摘要"
            assert 'num_assets' in results, "应包含资产数量"
            
            # 验证分析完成状态
            analysis_completed = results.get('analysis_completed', {})
            assert analysis_completed.get('sensitivity_analysis', False), "敏感性分析应完成"
            assert analysis_completed.get('marginal_risk_analysis', False), "边际风险分析应完成"
            
            print("   ✅ 权重影响分析测试通过")
            return True
            
        except Exception as e:
            print(f"   ❌ 权重影响分析测试失败: {e}")
            return False
    
    def test_portfolio_composition_analysis(self):
        """测试投资组合构成分析"""
        print("🧪 测试投资组合构成分析...")
        
        try:
            # 1. 测试单个投资组合分析
            single_results = self.risk_system.run_portfolio_composition_analysis(
                weights={"股票A": 0.3, "股票B": 0.2, "债券A": 0.3, "债券B": 0.2},
                analysis_type="single"
            )
            assert single_results.get('success', False), "单个分析应成功"
            
            # 2. 测试多投资组合分析
            multi_results = self.risk_system.run_portfolio_composition_analysis(
                analysis_type="multi",
                include_predefined=True
            )
            assert multi_results.get('success', False), "多组合分析应成功"
            
            # 3. 测试优化分析
            optimization_results = self.risk_system.run_portfolio_composition_analysis(
                weights={"股票A": 0.4, "股票B": 0.3, "债券A": 0.2, "债券B": 0.1},
                analysis_type="optimization"
            )
            assert optimization_results.get('success', False), "优化分析应成功"
            
            print("   ✅ 投资组合构成分析测试通过")
            return True
            
        except Exception as e:
            print(f"   ❌ 投资组合构成分析测试失败: {e}")
            return False
    
    def test_historical_backtest(self):
        """测试历史回溯测试"""
        print("🧪 测试历史回溯测试...")
        
        try:
            # 运行历史回溯测试（短期测试）
            from datetime import datetime, timedelta
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)  # 测试30天
            
            results = self.risk_system.run_historical_backtest(start_date, end_date)
            
            # 验证结果
            assert results.get('success', False), "历史回溯测试应成功"
            assert 'backtest_period' in results, "应包含回溯期间"
            assert 'files_generated' in results, "应生成文件"
            assert 'visualization_chart' in results['files_generated'], "应生成可视化图表"
            
            print("   ✅ 历史回溯测试通过")
            return True
            
        except Exception as e:
            print(f"   ❌ 历史回溯测试失败: {e}")
            return False
    
    def test_bootstrap_configuration(self):
        """测试Bootstrap方法配置"""
        print("🧪 测试Bootstrap方法配置...")
        
        try:
            # 测试配置加载
            from config.config_manager import config_manager
            config = config_manager.get_config()
            
            # 验证新增的配置项
            risk_metrics = config.get('risk_metrics', {})
            assert 'use_bootstrap_method' in risk_metrics, "应包含use_bootstrap_method配置"
            
            # 测试配置的影响
            original_value = risk_metrics.get('use_bootstrap_method', True)
            
            # 测试禁用bootstrap方法
            config_manager.update_parameter('risk_metrics', 'use_bootstrap_method', False)
            results = self.risk_system.run_quick_analysis('historical')
            assert results.get('success', False), "快速分析应成功"
            
            # 恢复原始配置
            config_manager.update_parameter('risk_metrics', 'use_bootstrap_method', original_value)
            
            print("   ✅ Bootstrap方法配置测试通过")
            return True
            
        except Exception as e:
            print(f"   ❌ Bootstrap方法配置测试失败: {e}")
            return False
    
    def test_output_directory_structure(self):
        """测试输出目录结构"""
        print("🧪 测试输出目录结构...")
        
        try:
            # 运行一个简单的分析来生成输出
            results = self.risk_system.run_quick_analysis()
            
            # 检查输出目录是否按新结构组织
            if results.get('success', False):
                # 验证输出管理器的功能
                output_dir = self.risk_system.output_manager.create_script_output_dir("test-analysis")
                
                # 验证子目录是否创建
                expected_subdirs = ['reports', 'charts', 'data', 'excel']
                for subdir in expected_subdirs:
                    subdir_path = output_dir / subdir
                    assert subdir_path.exists(), f"子目录 {subdir} 应存在"
                
                print("   ✅ 输出目录结构测试通过")
                return True
            else:
                print("   ⚠️  快速分析未成功，跳过输出目录测试")
                return True
                
        except Exception as e:
            print(f"   ❌ 输出目录结构测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 风险管理系统综合测试套件")
        print("=" * 60)
        
        # 设置测试环境
        if not self.setup_test_environment():
            return False
        
        # 定义测试列表
        tests = [
            ("权重配置管理器", self.test_weight_config_manager),
            ("权重影响分析", self.test_weight_impact_analysis),
            ("投资组合构成分析", self.test_portfolio_composition_analysis),
            ("历史回溯测试", self.test_historical_backtest),
            ("Bootstrap方法配置", self.test_bootstrap_configuration),
            ("输出目录结构", self.test_output_directory_structure)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 测试: {test_name}")
            print("-" * 40)
            
            try:
                if test_func():
                    passed += 1
                    self.test_results[test_name] = "PASSED"
                else:
                    self.test_results[test_name] = "FAILED"
                    print(f"❌ {test_name} 测试失败")
            except Exception as e:
                self.test_results[test_name] = f"ERROR: {e}"
                print(f"❌ {test_name} 测试出错: {e}")
        
        # 测试结果摘要
        print("\n" + "=" * 60)
        print(f"📊 测试结果摘要: {passed}/{total} 通过")
        
        # 详细结果
        print("\n📋 详细测试结果:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result == "PASSED" else "❌"
            print(f"   {status_icon} {test_name}: {result}")
        
        if passed == total:
            print("\n🎉 所有测试通过! 风险管理系统功能正常")
            return True
        else:
            print(f"\n⚠️  {total - passed} 个测试失败，请检查相关功能")
            return False


def main():
    """主测试函数"""
    test_suite = ComprehensiveTestSuite()
    success = test_suite.run_all_tests()
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
